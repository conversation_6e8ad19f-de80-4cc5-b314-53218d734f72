<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="db8b5f25-d3f2-4255-b8a3-6d475ece52d5" name="Changes" comment="feat(config): 调整访问令牌过期时间&#10;&#10;- 将访问令牌的过期时间从30 分钟调整为 20160 分钟&#10;- 此修改显著增加了令牌的有效期，提升了用户体验">
      <change beforePath="$PROJECT_DIR$/Excel模板使用说明.md" beforeDir="false" afterPath="$PROJECT_DIR$/Excel模板使用说明.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/utils/excel_handler.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/utils/excel_handler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docker-compose.yml" beforeDir="false" afterPath="$PROJECT_DIR$/docker-compose.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/API_COMPLETE_DOCUMENTATION.md" beforeDir="false" afterPath="$PROJECT_DIR$/docs/API_COMPLETE_DOCUMENTATION.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/generate_excel_template.py" beforeDir="false" afterPath="$PROJECT_DIR$/generate_excel_template.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/app.log" beforeDir="false" afterPath="$PROJECT_DIR$/logs/app.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/start.py" beforeDir="false" afterPath="$PROJECT_DIR$/start.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../../../go/server/go1.22.2" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30YMmWunNTDkWlTPetish0WzfN0" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.automatic.dependencies.download&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.go.list.on.any.changes.was.set&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;go.sdk.automatically.set&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/h5_code/yysls/yysls-ranking-backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;http.proxy&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\h5_code\yysls\yysls-ranking-backend\ssh" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: 新增内容管理和系统配置模型" />
    <MESSAGE value="feat: 为榜单类型添加中文名称显示" />
    <MESSAGE value="feat(auth): 支持微信小程序登录" />
    <MESSAGE value="feat: 为未授权用户设置默认头像" />
    <MESSAGE value="refactor: 修改用户性别枚举值及约束" />
    <MESSAGE value="feat(auth): 扩展管理员权限包括超级管理员&#10;&#10;- 修改了多个文件中的权限检查逻辑，允许超级管理员拥有与管理员相同的权限&#10;- 更新了相关函数和API端点的注释，明确支持管理员和超级管理员&#10;- 调整了获取管理员列表和检查管理员权限的逻辑，包含超级管理员在内" />
    <MESSAGE value="fix: 重构系统配置相关代码&#10;&#10;- 修改了系统配置的模型、schema和服务层逻辑" />
    <MESSAGE value="fix: 更新系统配置接口" />
    <MESSAGE value="fix: 优化配置缓存刷新逻辑" />
    <MESSAGE value="fix: 修改用户更新接口并优化用户模型" />
    <MESSAGE value="feat: 优化用户编号生成逻辑&#10;&#10;- 引入新的用户编号验证函数，确保编号格式正确&#10;- 新增用户编号生成逻辑，基于用户ID生成唯一编号- 在用户创建和微信注册流程中集成用户编号生成" />
    <MESSAGE value="feat: 重构内容模型并优化内容管理功能" />
    <MESSAGE value="fix: 重构 Content 模型以匹配 MySQL 表结构" />
    <MESSAGE value="refactor(api): 更新 API端点以使用异步数据库会话&#10;&#10;- 将 Session 替换为 AsyncSession&#10;- 使用 model_validate 替代 from_orm- 启用系统配置和内容管理路由" />
    <MESSAGE value="fix: 更新 API端点以使用异步数据库会话&#10;&#10;- 将 Session 替换为 AsyncSession&#10;- 使用 model_validate 替代 from_orm- 启用系统配置和内容管理路由" />
    <MESSAGE value="fix(api): 修复文件上传目录和权限问题&#10;&#10;- 修改上传目录为绝对路径，解决相对路径问题&#10;- 添加目录创建时的权限设置，避免权限错误&#10;-引入静态文件配置，支持静态资源管理&#10;- 优化 Docker部署配置，使用外部卷存储上传文件&#10;- 修改 Dockerfile，创建必要目录并设置权限" />
    <MESSAGE value="feat: 启用静态文件服务并优化目录路径处理" />
    <MESSAGE value="fix: 更新默认头像链接更新用户未授权时的默认头像链接，以解决头像显示问题。" />
    <MESSAGE value="feat(api): 添加用户反馈功能- 新增反馈路由和相关模型、schema&#10;- 在用户模型中添加反馈关系&#10;- 创建反馈数据库表&#10;- 更新API文档，增加反馈相关接口" />
    <MESSAGE value="fix: 移除用户编号验证器" />
    <MESSAGE value="fix: 移除微信登录时更新用户昵称和头像的逻辑&#10;&#10;- 删除了在微信登录时更新用户昵称和头像的代码&#10;-保留了更新最后登录时间的逻辑" />
    <MESSAGE value="fix: 修改更新榜单接口路径&#10;&#10;- 将更新榜单接口的路径从&quot;/{ranking_id}&quot;改为&quot;&quot;&#10;- 优化了接口设计，去掉了不必要的路径参数" />
    <MESSAGE value="feat: 实现通用文件上传功能&#10;&#10;- 新增支持Excel和图片文件的上传接口&#10;-优化文件验证和处理逻辑&#10;- 添加缩略图生成和文件信息返回功能&#10;- 更新配置文件和环境变量示例&#10;- 新增图片处理工具类" />
    <MESSAGE value="feat: 添加用户头像显示功能&#10;&#10;- 在反馈列表和详情中增加用户头像URL字段&#10;- 新增上传头像功能" />
    <MESSAGE value="feat(config): 调整访问令牌过期时间&#10;&#10;- 将访问令牌的过期时间从30 分钟调整为 20160 分钟&#10;- 此修改显著增加了令牌的有效期，提升了用户体验" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(config): 调整访问令牌过期时间&#10;&#10;- 将访问令牌的过期时间从30 分钟调整为 20160 分钟&#10;- 此修改显著增加了令牌的有效期，提升了用户体验" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>