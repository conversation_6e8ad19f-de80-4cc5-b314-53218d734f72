#!/usr/bin/env python3
"""
测试反馈列表头像字段返回
"""
import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
LOGIN_URL = f"{BASE_URL}/api/v1/auth/login"
FEEDBACK_LIST_URL = f"{BASE_URL}/api/v1/feedback/admin/list"
CREATE_FEEDBACK_URL = f"{BASE_URL}/api/v1/feedback"

def login_and_get_token(username="admin", password="admin123"):
    """登录获取token"""
    login_data = {
        "username": username,
        "password": password
    }
    
    response = requests.post(LOGIN_URL, json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data["data"]["access_token"]
    else:
        print(f"登录失败: {response.text}")
        return None

def create_test_feedback(token):
    """创建测试反馈"""
    print("创建测试反馈...")
    
    headers = {"Authorization": f"Bearer {token}"}
    feedback_data = {
        "content": "这是一个测试反馈，用于验证头像字段返回"
    }
    
    response = requests.post(CREATE_FEEDBACK_URL, headers=headers, json=feedback_data)
    
    if response.status_code == 200:
        data = response.json()
        print("✓ 反馈创建成功")
        print(f"  反馈ID: {data['data']['id']}")
        print(f"  用户昵称: {data['data']['user_nickname']}")
        print(f"  头像URL: {data['data']['avatar_url']}")
        return data['data']
    else:
        print(f"✗ 反馈创建失败: {response.text}")
        return None

def test_feedback_list_with_avatar(admin_token):
    """测试反馈列表是否包含头像字段"""
    print("\n测试反馈列表头像字段...")
    
    headers = {"Authorization": f"Bearer {admin_token}"}
    
    response = requests.get(FEEDBACK_LIST_URL, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✓ 获取反馈列表成功")
        
        if data['data']['items']:
            for i, feedback in enumerate(data['data']['items'][:3]):  # 只显示前3个
                print(f"\n  反馈 {i+1}:")
                print(f"    ID: {feedback['id']}")
                print(f"    内容: {feedback['content'][:50]}...")
                print(f"    用户ID: {feedback['user_id']}")
                print(f"    用户昵称: {feedback.get('user_nickname', 'N/A')}")
                print(f"    用户名: {feedback.get('user_username', 'N/A')}")
                print(f"    头像URL: {feedback.get('avatar_url', 'N/A')}")
                print(f"    状态: {feedback['status']}")
                
                # 检查头像字段是否存在
                if 'avatar_url' in feedback:
                    print("    ✓ 头像字段存在")
                    if feedback['avatar_url']:
                        print("    ✓ 头像URL不为空")
                        # 测试头像URL是否可访问
                        if feedback['avatar_url'].startswith('http'):
                            try:
                                avatar_response = requests.head(feedback['avatar_url'], timeout=5)
                                if avatar_response.status_code == 200:
                                    print("    ✓ 头像URL可访问")
                                else:
                                    print(f"    ⚠ 头像URL不可访问: {avatar_response.status_code}")
                            except Exception as e:
                                print(f"    ⚠ 头像URL访问测试失败: {str(e)}")
                    else:
                        print("    ⚠ 头像URL为空")
                else:
                    print("    ✗ 头像字段不存在")
        else:
            print("  没有反馈数据")
            
        return data['data']
    else:
        print(f"✗ 获取反馈列表失败: {response.text}")
        return None

def test_my_feedback_list_with_avatar(user_token):
    """测试我的反馈列表是否包含头像字段"""
    print("\n测试我的反馈列表头像字段...")
    
    headers = {"Authorization": f"Bearer {user_token}"}
    my_feedback_url = f"{BASE_URL}/api/v1/feedback/my"
    
    response = requests.get(my_feedback_url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✓ 获取我的反馈列表成功")
        
        if data['data']['items']:
            feedback = data['data']['items'][0]  # 只检查第一个
            print(f"\n  我的反馈:")
            print(f"    ID: {feedback['id']}")
            print(f"    内容: {feedback['content'][:50]}...")
            print(f"    用户昵称: {feedback.get('user_nickname', 'N/A')}")
            print(f"    头像URL: {feedback.get('avatar_url', 'N/A')}")
            
            # 检查头像字段
            if 'avatar_url' in feedback:
                print("    ✓ 头像字段存在")
            else:
                print("    ✗ 头像字段不存在")
        else:
            print("  没有我的反馈数据")
            
        return data['data']
    else:
        print(f"✗ 获取我的反馈列表失败: {response.text}")
        return None

def main():
    """主测试函数"""
    print("=== 反馈列表头像字段测试 ===")
    
    # 获取管理员token
    admin_token = login_and_get_token("admin", "admin123")
    if not admin_token:
        print("无法获取管理员token，测试终止")
        return
    
    print("✓ 管理员登录成功")
    
    # 获取普通用户token（如果存在）
    user_token = login_and_get_token("testuser", "password123")
    if user_token:
        print("✓ 普通用户登录成功")
        
        # 创建测试反馈
        create_test_feedback(user_token)
        
        # 测试我的反馈列表
        test_my_feedback_list_with_avatar(user_token)
    else:
        print("⚠ 普通用户登录失败，跳过用户反馈测试")
    
    # 测试管理员反馈列表
    test_feedback_list_with_avatar(admin_token)
    
    print("\n=== 测试完成 ===")
    print("\n预期结果:")
    print("1. 反馈响应中应包含 avatar_url 字段")
    print("2. 如果用户有头像，avatar_url 应为完整的URL")
    print("3. 如果用户没有头像，avatar_url 应为 null")
    print("4. 匿名反馈的 avatar_url 应为 null")

if __name__ == "__main__":
    main()
