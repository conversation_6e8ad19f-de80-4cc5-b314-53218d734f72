"""
榜单管理API端点

提供榜单的创建、查询、更新、删除等功能
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.ranking_service import RankingService
from app.schemas.ranking import (
    RankingCreate, RankingUpdate, RankingResponse,
    RankingDetailCreate, RankingDetailUpdate, RankingDetailResponse,
    get_ranking_type_display_name
)
from app.schemas.common import ResponseModel, PaginatedResponse
from app.utils.security import verify_token
from app.models.ranking import RankingStatus, RankingType

router = APIRouter()
security = HTTPBearer()
ranking_service = RankingService()


async def get_current_user_id(credentials: HTTPAuthorizationCredentials = Depends(security)) -> int:
    """获取当前用户ID"""
    payload = verify_token(credentials.credentials)
    return int(payload.get("sub"))


@router.get("", response_model=ResponseModel[PaginatedResponse[RankingResponse]], summary="获取榜单列表")
async def get_rankings(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    status: Optional[RankingStatus] = Query(None, description="榜单状态筛选"),
    ranking_type: Optional[RankingType] = Query(None, description="榜单类型筛选"),
    db: Session = Depends(get_db)
) -> ResponseModel[PaginatedResponse[RankingResponse]]:
    """
    获取榜单列表
    
    - **page**: 页码，从1开始
    - **size**: 每页大小，最大100
    - **status**: 榜单状态筛选（可选）
    - **ranking_type**: 榜单类型筛选（可选）
    """
    try:
        # 构建筛选条件
        filters = {}
        if status:
            filters["status"] = status
        if ranking_type:
            filters["ranking_type"] = ranking_type
        
        # 获取榜单列表
        rankings, total = ranking_service.get_multi_with_total(
            db, skip=(page - 1) * size, limit=size, **filters
        )
        
        # 转换为响应模型
        ranking_responses = []
        for ranking in rankings:
            ranking_data = RankingResponse.model_validate(ranking)
            # 确保设置榜单类型中文名称
            ranking_data.ranking_type_name = get_ranking_type_display_name(ranking.ranking_type)
            # 设置固定返回值
            ranking_data.auto_import_details = True
            ranking_data.replace_existing_details = True
            ranking_responses.append(ranking_data)
        
        # 构建分页响应
        pages = (total + size - 1) // size
        paginated_data = PaginatedResponse(
            items=ranking_responses,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
        return ResponseModel(
            code=200,
            message="获取榜单列表成功",
            data=paginated_data
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取榜单列表失败: {str(e)}"
        )


@router.post("", response_model=ResponseModel[RankingResponse], summary="创建榜单")
async def create_ranking(
    ranking_data: RankingCreate,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
) -> ResponseModel[RankingResponse]:
    """
    创建新榜单

    需要管理员权限

    支持Excel文件导入榜单明细：
    - excel_file_path: 通过 /api/v1/upload/excel/upload-temp 上传的临时文件路径
    - auto_import_details: 是否自动导入Excel中的榜单明细数据
    """
    try:
        # 转换为字典
        ranking_dict = ranking_data.dict()

        # 检查是否需要Excel导入
        if ranking_dict.get('auto_import_details') and ranking_dict.get('excel_file_path'):
            # 使用带Excel导入的创建方法
            ranking = await ranking_service.create_ranking_with_excel(
                db=db,
                ranking_data=ranking_dict,
                created_by=current_user_id,
                excel_file_path=ranking_dict.get('excel_file_path'),
                auto_import_details=ranking_dict.get('auto_import_details', False)
            )
            message = "创建榜单成功，已导入Excel数据"
        else:
            # 普通创建方法
            ranking = ranking_service.create_ranking(db, ranking_dict, current_user_id)
            message = "创建榜单成功"

        # 转换为响应模型并设置中文名称
        ranking_data = RankingResponse.model_validate(ranking)
        ranking_data.ranking_type_name = get_ranking_type_display_name(ranking.ranking_type)

        return ResponseModel(
            code=200,
            message=message,
            data=ranking_data
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建榜单失败: {str(e)}"
        )


@router.get("/{ranking_id}", response_model=ResponseModel[RankingResponse], summary="获取榜单详情")
async def get_ranking(
    ranking_id: int,
    db: Session = Depends(get_db)
) -> ResponseModel[RankingResponse]:
    """
    获取指定榜单的详细信息
    """
    try:
        ranking = ranking_service.get(db, ranking_id)
        if not ranking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="榜单不存在"
            )
        
        # 转换为响应模型并设置中文名称
        ranking_data = RankingResponse.model_validate(ranking)
        ranking_data.ranking_type_name = get_ranking_type_display_name(ranking.ranking_type)

        return ResponseModel(
            code=200,
            message="获取榜单详情成功",
            data=ranking_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取榜单详情失败: {str(e)}"
        )


@router.put("", response_model=ResponseModel[RankingResponse], summary="更新榜单")
async def update_ranking(
    ranking_data: RankingUpdate,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
) -> ResponseModel[RankingResponse]:
    """
    更新榜单信息

    需要管理员权限

    支持Excel文件导入榜单明细：
    - excel_file_path: 通过 /api/v1/upload/excel/upload-temp 上传的临时文件路径
    - auto_import_details: 是否自动导入Excel中的榜单明细数据
    - replace_existing_details: 是否替换现有的榜单明细数据
    """
    try:
        # 检查榜单是否存在
        existing_ranking = ranking_service.get(db, ranking_data.id)
        if not existing_ranking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="榜单不存在"
            )

        # 转换为字典
        ranking_dict = ranking_data.model_dump(exclude_unset=True)

        # 检查是否需要Excel导入
        if ranking_dict.get('auto_import_details') and ranking_dict.get('excel_file_path'):
            # 使用带Excel导入的更新方法
            ranking = await ranking_service.update_ranking_with_excel(
                db=db,
                ranking_id=ranking_data.id,
                ranking_data=ranking_dict,
                updated_by=current_user_id,
                excel_file_path=ranking_dict.get('excel_file_path'),
                auto_import_details=ranking_dict.get('auto_import_details', False),
                replace_existing_details=ranking_dict.get('replace_existing_details', False)
            )
            message = "更新榜单成功，已导入Excel数据"
        else:
            # 普通更新方法
            ranking = ranking_service.update(db, db_obj=existing_ranking, obj_in=ranking_data)
            message = "更新榜单成功"

        # 转换为响应模型并设置中文名称
        ranking_data = RankingResponse.model_validate(ranking)
        ranking_data.ranking_type_name = get_ranking_type_display_name(ranking.ranking_type)

        return ResponseModel(
            code=200,
            message=message,
            data=ranking_data
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新榜单失败: {str(e)}"
        )


@router.delete("/{ranking_id}", response_model=ResponseModel[None], summary="删除榜单")
async def delete_ranking(
    ranking_id: int,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
) -> ResponseModel[None]:
    """
    删除榜单
    
    需要管理员权限
    """
    try:
        # 检查榜单是否存在
        ranking = ranking_service.get(db, ranking_id)
        if not ranking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="榜单不存在"
            )

        # 删除榜单
        ranking_service.remove(db, id=ranking_id)
        
        return ResponseModel(
            code=200,
            message="删除榜单成功",
            data=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除榜单失败: {str(e)}"
        )


@router.get("/{ranking_id}/details", response_model=ResponseModel[List[RankingDetailResponse]], summary="获取榜单明细")
async def get_ranking_details(
    ranking_id: int,
    db: Session = Depends(get_db)
) -> ResponseModel[List[RankingDetailResponse]]:
    """
    获取指定榜单的明细记录
    """
    try:
        # 检查榜单是否存在
        ranking = ranking_service.get(db, ranking_id)
        if not ranking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="榜单不存在"
            )

        # 获取榜单明细
        details = ranking_service.get_ranking_details(db, ranking_id)
        
        # 转换为响应模型
        detail_responses = [RankingDetailResponse.model_validate(detail) for detail in details]
        
        return ResponseModel(
            code=200,
            message="获取榜单明细成功",
            data=detail_responses
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取榜单明细失败: {str(e)}"
        )


@router.post("/{ranking_id}/details", response_model=ResponseModel[RankingDetailResponse], summary="添加榜单明细")
async def add_ranking_detail(
    ranking_id: int,
    detail_data: RankingDetailCreate,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
) -> ResponseModel[RankingDetailResponse]:
    """
    为榜单添加明细记录
    
    需要管理员权限
    """
    try:
        # 检查榜单是否存在
        ranking = ranking_service.get(db, ranking_id)
        if not ranking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="榜单不存在"
            )

        # 添加榜单明细
        detail = ranking_service.add_ranking_detail(db, ranking_id, detail_data.model_dump())
        
        return ResponseModel(
            code=200,
            message="添加榜单明细成功",
            data=RankingDetailResponse.model_validate(detail)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加榜单明细失败: {str(e)}"
        )


@router.put("/{ranking_id}/status", response_model=ResponseModel[RankingResponse], summary="更新榜单状态")
async def update_ranking_status(
    ranking_id: int,
    new_status: RankingStatus,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
) -> ResponseModel[RankingResponse]:
    """
    更新榜单状态
    
    需要管理员权限
    """
    try:
        # 检查榜单是否存在
        ranking = ranking_service.get(db, ranking_id)
        if not ranking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="榜单不存在"
            )
        
        # 更新榜单状态
        updated_ranking = ranking_service.update_status(db, ranking_id, new_status)

        # 转换为响应模型并设置中文名称
        ranking_data = RankingResponse.model_validate(updated_ranking)
        ranking_data.ranking_type_name = get_ranking_type_display_name(updated_ranking.ranking_type)

        return ResponseModel(
            code=200,
            message="更新榜单状态成功",
            data=ranking_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新榜单状态失败: {str(e)}"
        )


@router.put("/details/{detail_id}", response_model=ResponseModel[RankingDetailResponse], summary="更新榜单明细")
async def update_ranking_detail(
    detail_id: int,
    detail_data: RankingDetailUpdate,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
) -> ResponseModel[RankingDetailResponse]:
    """
    更新榜单明细记录

    需要管理员权限
    """
    try:
        # 更新榜单明细
        detail = ranking_service.update_ranking_detail(
            db, detail_id, detail_data.model_dump(exclude_unset=True)
        )

        return ResponseModel(
            code=200,
            message="更新榜单明细成功",
            data=RankingDetailResponse.model_validate(detail)
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新榜单明细失败: {str(e)}"
        )
