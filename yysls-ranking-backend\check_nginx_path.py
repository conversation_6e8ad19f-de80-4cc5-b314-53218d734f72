#!/usr/bin/env python3
"""
检查Nginx配置路径是否正确
"""
import os
import subprocess
from app.config import settings

def check_paths():
    """检查路径配置"""
    print("=== 路径检查 ===")
    
    # 1. 检查当前工作目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 2. 检查项目uploads目录
    uploads_dir = settings.upload_dir_absolute
    print(f"项目uploads目录: {uploads_dir}")
    print(f"uploads目录存在: {os.path.exists(uploads_dir)}")
    
    # 3. 检查目标文件
    target_file = os.path.join(uploads_dir, "images", "temp", "temp_20250805_000519_138ca98b_1.jpg")
    print(f"目标文件路径: {target_file}")
    print(f"目标文件存在: {os.path.exists(target_file)}")
    
    if os.path.exists(target_file):
        file_size = os.path.getsize(target_file)
        print(f"文件大小: {file_size} bytes")
    
    # 4. 检查可能的部署路径
    possible_paths = [
        "/app/uploads/",
        "/home/<USER>/yysls-ranking-backend/uploads/",
        "/var/www/yysls-ranking-backend/uploads/",
        "/opt/yysls-ranking-backend/uploads/",
        uploads_dir
    ]
    
    print(f"\n=== 可能的部署路径检查 ===")
    for path in possible_paths:
        exists = os.path.exists(path)
        print(f"{path} - {'存在' if exists else '不存在'}")
        
        if exists:
            # 检查是否有目标文件
            test_file = os.path.join(path, "images", "temp", "temp_20250805_000519_138ca98b_1.jpg")
            file_exists = os.path.exists(test_file)
            print(f"  └── 目标文件: {'存在' if file_exists else '不存在'}")

def generate_nginx_config():
    """生成正确的Nginx配置"""
    uploads_dir = settings.upload_dir_absolute
    
    print(f"\n=== 建议的Nginx配置 ===")
    print(f"基于当前项目路径: {uploads_dir}")
    
    nginx_config = f"""
# 静态文件服务 - 修正后的配置
location /uploads/ {{
    alias {uploads_dir}/;
    expires 30d;
    add_header Cache-Control "public, no-transform";
    
    # 允许跨域访问
    add_header Access-Control-Allow-Origin "*";
    add_header Access-Control-Allow-Methods "GET, OPTIONS";
    
    # 安全设置
    location ~* \\.(php|jsp|cgi|asp|aspx)$ {{
        deny all;
    }}
    
    # 文件不存在时返回404
    try_files $uri =404;
}}"""
    
    print(nginx_config)
    
    # 保存到文件
    with open("nginx_uploads_config.txt", "w", encoding="utf-8") as f:
        f.write(nginx_config)
    
    print(f"\n配置已保存到: nginx_uploads_config.txt")

def test_file_permissions():
    """测试文件权限"""
    print(f"\n=== 文件权限检查 ===")
    
    uploads_dir = settings.upload_dir_absolute
    target_file = os.path.join(uploads_dir, "images", "temp", "temp_20250805_000519_138ca98b_1.jpg")
    
    if os.path.exists(target_file):
        # 获取文件权限
        stat_info = os.stat(target_file)
        permissions = oct(stat_info.st_mode)[-3:]
        print(f"文件权限: {permissions}")
        
        # 检查是否可读
        readable = os.access(target_file, os.R_OK)
        print(f"文件可读: {readable}")
        
        # 检查目录权限
        dir_path = os.path.dirname(target_file)
        dir_stat = os.stat(dir_path)
        dir_permissions = oct(dir_stat.st_mode)[-3:]
        print(f"目录权限: {dir_permissions}")
        
        dir_readable = os.access(dir_path, os.R_OK)
        dir_executable = os.access(dir_path, os.X_OK)
        print(f"目录可读: {dir_readable}")
        print(f"目录可执行: {dir_executable}")
        
        # 建议的权限设置
        print(f"\n建议的权限设置:")
        print(f"文件: chmod 644 {target_file}")
        print(f"目录: chmod 755 {dir_path}")
    else:
        print("目标文件不存在，无法检查权限")

def main():
    """主函数"""
    print("=== Nginx配置路径检查工具 ===")
    
    check_paths()
    generate_nginx_config()
    test_file_permissions()
    
    print(f"\n=== 解决步骤 ===")
    print("1. 使用上面生成的正确Nginx配置替换现有配置")
    print("2. 检查并修正文件权限")
    print("3. 重新加载Nginx配置: sudo nginx -t && sudo systemctl reload nginx")
    print("4. 测试文件访问")

if __name__ == "__main__":
    main()
