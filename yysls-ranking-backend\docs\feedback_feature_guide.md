# 用户反馈功能使用指南

## 功能概述

用户反馈功能是燕友圈榜单系统的重要组成部分，允许用户（包括匿名用户）向系统管理员提交反馈意见，管理员可以查看、处理和回复这些反馈。

## 功能特点

### 1. 支持匿名反馈
- 用户无需登录即可提交反馈
- 登录用户的反馈会自动关联用户信息
- 保护用户隐私，鼓励真实反馈

### 2. 完整的反馈生命周期管理
- **待处理** (pending): 新提交的反馈
- **处理中** (processing): 管理员正在处理的反馈
- **已处理** (resolved): 已解决的反馈
- **已忽略** (ignored): 管理员标记为忽略的反馈

### 3. 管理员功能
- 查看所有反馈列表
- 按状态、用户等条件筛选反馈
- 更新反馈状态
- 添加管理员回复
- 查看反馈统计信息

### 4. 用户功能
- 提交反馈（支持匿名）
- 查看自己的反馈历史（需登录）
- 查看管理员回复

## 数据模型

### Feedback 反馈表

```sql
CREATE TABLE feedback (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '反馈ID',
    content TEXT NOT NULL COMMENT '反馈内容',
    user_id INT NULL COMMENT '用户ID，可为空支持匿名反馈',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '反馈状态',
    admin_reply TEXT NULL COMMENT '管理员回复',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX ix_feedback_status (status),
    INDEX ix_feedback_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户反馈表';
```

## API接口详解

### 1. 用户端接口

#### 提交反馈
```http
POST /api/v1/feedback
Content-Type: application/json

{
  "content": "反馈内容，最大1000字符",
  "user_id": null  // 可选，匿名反馈时为null
}
```

**特点**:
- 支持匿名提交（不需要认证）
- 如果用户已登录，会自动关联用户信息
- 内容长度限制1000字符
- 自动设置状态为"待处理"

#### 查看我的反馈
```http
GET /api/v1/feedback/my?page=1&page_size=20
Authorization: Bearer {token}
```

**特点**:
- 需要用户登录
- 只能查看自己提交的反馈
- 支持分页查询
- 包含管理员回复信息

### 2. 管理员端接口

#### 获取反馈列表
```http
GET /api/v1/feedback/admin/list?status=pending&page=1&page_size=20
Authorization: Bearer {admin_token}
```

**筛选条件**:
- `status`: 按状态筛选
- `user_id`: 按用户ID筛选
- `page`: 页码
- `page_size`: 每页数量
- `order_by`: 排序字段
- `order_desc`: 是否降序

#### 获取反馈详情
```http
GET /api/v1/feedback/admin/{feedback_id}
Authorization: Bearer {admin_token}
```

#### 更新反馈
```http
PUT /api/v1/feedback/admin/{feedback_id}
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "status": "resolved",
  "admin_reply": "问题已解决，感谢您的反馈！"
}
```

#### 获取反馈统计
```http
GET /api/v1/feedback/admin/stats
Authorization: Bearer {admin_token}
```

**统计信息包括**:
- 总反馈数
- 各状态反馈数量
- 今日新增反馈数
- 本周新增反馈数

## 使用场景

### 1. 用户提交反馈

**场景**: 用户在使用系统过程中遇到问题或有建议

**流程**:
1. 用户访问反馈页面
2. 填写反馈内容
3. 选择是否匿名提交
4. 提交反馈
5. 系统返回反馈ID和提交成功信息

**代码示例**:
```javascript
// 提交反馈
const submitFeedback = async (content, isAnonymous = false) => {
  const response = await fetch('/api/v1/feedback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(isAnonymous ? {} : { 'Authorization': `Bearer ${token}` })
    },
    body: JSON.stringify({
      content: content,
      user_id: null
    })
  });
  
  const result = await response.json();
  if (result.code === 200) {
    console.log('反馈提交成功:', result.data);
  }
};
```

### 2. 管理员处理反馈

**场景**: 管理员查看和处理用户反馈

**流程**:
1. 管理员登录系统
2. 查看反馈列表
3. 筛选待处理反馈
4. 查看反馈详情
5. 更新反馈状态并添加回复
6. 保存更新

**代码示例**:
```javascript
// 管理员处理反馈
const processFeedback = async (feedbackId, status, reply) => {
  const response = await fetch(`/api/v1/feedback/admin/${feedbackId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${adminToken}`
    },
    body: JSON.stringify({
      status: status,
      admin_reply: reply
    })
  });
  
  const result = await response.json();
  if (result.code === 200) {
    console.log('反馈处理成功:', result.data);
  }
};
```

### 3. 反馈统计分析

**场景**: 管理员查看反馈统计，了解系统使用情况

**流程**:
1. 管理员访问统计页面
2. 查看各类统计数据
3. 分析反馈趋势
4. 制定改进计划

## 最佳实践

### 1. 反馈内容规范
- 鼓励用户提供具体、详细的反馈
- 建议包含问题复现步骤
- 提供联系方式以便进一步沟通

### 2. 管理员处理规范
- 及时响应用户反馈
- 提供清晰、友好的回复
- 合理设置反馈状态
- 定期分析反馈趋势

### 3. 系统维护
- 定期清理过期反馈
- 监控反馈处理效率
- 优化反馈分类和标签
- 建立反馈处理SLA

## 扩展功能建议

### 1. 反馈分类
- 添加反馈类型字段（bug报告、功能建议、使用问题等）
- 支持按类型筛选和统计

### 2. 反馈评分
- 用户可以对管理员回复进行评分
- 统计管理员处理满意度

### 3. 反馈通知
- 邮件通知用户反馈处理结果
- 管理员新反馈提醒

### 4. 反馈导出
- 支持导出反馈数据
- 生成反馈分析报告

## 技术实现要点

### 1. 数据验证
- 反馈内容长度限制
- 状态值有效性检查
- 用户权限验证

### 2. 性能优化
- 反馈列表分页查询
- 数据库索引优化
- 缓存热门查询

### 3. 安全考虑
- 防止恶意提交
- 敏感信息过滤
- 管理员权限控制

### 4. 监控告警
- 反馈提交量监控
- 处理时效监控
- 异常情况告警
