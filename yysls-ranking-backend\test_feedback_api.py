#!/usr/bin/env python3
"""
反馈功能API测试脚本

用于快速测试反馈功能的各个接口
"""
import requests
import json
import sys
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8000/api/v1"
ADMIN_TOKEN = ""  # 需要填入实际的管理员token
USER_TOKEN = ""   # 需要填入实际的用户token

def print_response(response, title="响应"):
    """打印响应信息"""
    print(f"\n=== {title} ===")
    print(f"状态码: {response.status_code}")
    try:
        data = response.json()
        print(f"响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
    except:
        print(f"响应内容: {response.text}")
    print("=" * 50)

def test_anonymous_feedback():
    """测试匿名反馈提交"""
    print("\n🔍 测试匿名反馈提交")
    
    url = f"{BASE_URL}/feedback"
    data = {
        "content": "这是一个匿名反馈测试，系统整体运行良好！",
        "user_id": None
    }
    
    response = requests.post(url, json=data)
    print_response(response, "匿名反馈提交")
    
    if response.status_code == 200:
        result = response.json()
        return result.get("data", {}).get("id")
    return None

def test_user_feedback():
    """测试登录用户反馈提交"""
    if not USER_TOKEN:
        print("\n⚠️  跳过用户反馈测试（未提供用户token）")
        return None
        
    print("\n🔍 测试登录用户反馈提交")
    
    url = f"{BASE_URL}/feedback"
    headers = {"Authorization": f"Bearer {USER_TOKEN}"}
    data = {
        "content": "作为登录用户，我希望能看到更多的榜单统计信息。",
        "user_id": None
    }
    
    response = requests.post(url, json=data, headers=headers)
    print_response(response, "用户反馈提交")
    
    if response.status_code == 200:
        result = response.json()
        return result.get("data", {}).get("id")
    return None

def test_get_my_feedbacks():
    """测试获取我的反馈列表"""
    if not USER_TOKEN:
        print("\n⚠️  跳过我的反馈列表测试（未提供用户token）")
        return
        
    print("\n🔍 测试获取我的反馈列表")
    
    url = f"{BASE_URL}/feedback/my"
    headers = {"Authorization": f"Bearer {USER_TOKEN}"}
    params = {"page": 1, "page_size": 10}
    
    response = requests.get(url, headers=headers, params=params)
    print_response(response, "我的反馈列表")

def test_admin_get_feedbacks():
    """测试管理员获取反馈列表"""
    if not ADMIN_TOKEN:
        print("\n⚠️  跳过管理员反馈列表测试（未提供管理员token）")
        return
        
    print("\n🔍 测试管理员获取反馈列表")
    
    url = f"{BASE_URL}/feedback/admin/list"
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    params = {
        "page": 1,
        "page_size": 10,
        "status": "pending"
    }
    
    response = requests.get(url, headers=headers, params=params)
    print_response(response, "管理员反馈列表")

def test_admin_get_feedback_detail(feedback_id):
    """测试管理员获取反馈详情"""
    if not ADMIN_TOKEN or not feedback_id:
        print("\n⚠️  跳过管理员反馈详情测试（未提供管理员token或反馈ID）")
        return
        
    print(f"\n🔍 测试管理员获取反馈详情 (ID: {feedback_id})")
    
    url = f"{BASE_URL}/feedback/admin/{feedback_id}"
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    
    response = requests.get(url, headers=headers)
    print_response(response, "反馈详情")

def test_admin_update_feedback(feedback_id):
    """测试管理员更新反馈"""
    if not ADMIN_TOKEN or not feedback_id:
        print("\n⚠️  跳过管理员更新反馈测试（未提供管理员token或反馈ID）")
        return
        
    print(f"\n🔍 测试管理员更新反馈 (ID: {feedback_id})")
    
    url = f"{BASE_URL}/feedback/admin/{feedback_id}"
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    data = {
        "status": "resolved",
        "admin_reply": f"感谢您的反馈！我们已经记录了您的建议，会在后续版本中考虑实现。回复时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    }
    
    response = requests.put(url, json=data, headers=headers)
    print_response(response, "更新反馈")

def test_admin_get_stats():
    """测试管理员获取反馈统计"""
    if not ADMIN_TOKEN:
        print("\n⚠️  跳过管理员统计测试（未提供管理员token）")
        return
        
    print("\n🔍 测试管理员获取反馈统计")
    
    url = f"{BASE_URL}/feedback/admin/stats"
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    
    response = requests.get(url, headers=headers)
    print_response(response, "反馈统计")

def test_invalid_feedback():
    """测试无效反馈提交"""
    print("\n🔍 测试无效反馈提交（空内容）")
    
    url = f"{BASE_URL}/feedback"
    data = {
        "content": "",  # 空内容
        "user_id": None
    }
    
    response = requests.post(url, json=data)
    print_response(response, "无效反馈提交")

def main():
    """主测试函数"""
    print("🚀 开始测试反馈功能API")
    print(f"基础URL: {BASE_URL}")
    print(f"管理员Token: {'已设置' if ADMIN_TOKEN else '未设置'}")
    print(f"用户Token: {'已设置' if USER_TOKEN else '未设置'}")
    
    # 测试匿名反馈
    feedback_id = test_anonymous_feedback()
    
    # 测试用户反馈
    user_feedback_id = test_user_feedback()
    
    # 测试获取我的反馈
    test_get_my_feedbacks()
    
    # 测试管理员功能
    test_admin_get_feedbacks()
    
    # 使用第一个反馈ID进行详情和更新测试
    test_feedback_id = feedback_id or user_feedback_id
    if test_feedback_id:
        test_admin_get_feedback_detail(test_feedback_id)
        test_admin_update_feedback(test_feedback_id)
    
    # 测试统计
    test_admin_get_stats()
    
    # 测试无效输入
    test_invalid_feedback()
    
    print("\n✅ 反馈功能API测试完成！")
    print("\n📝 使用说明：")
    print("1. 请在脚本顶部设置正确的ADMIN_TOKEN和USER_TOKEN")
    print("2. 确保服务器正在运行在 http://localhost:8000")
    print("3. 确保数据库已正确迁移并包含feedback表")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {str(e)}")
        sys.exit(1)
