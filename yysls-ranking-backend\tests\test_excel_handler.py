"""
Excel处理功能测试
"""
import os
import tempfile
from io import BytesIO
from datetime import time

import pytest
from fastapi import UploadFile
from openpyxl import Workbook

from app.utils.excel_handler import ExcelHandler


class TestExcelHandler:
    """Excel处理工具类测试"""

    def setup_method(self):
        """测试前准备"""
        self.excel_handler = ExcelHandler()

    def test_parse_shorthand_time_basic_seconds(self):
        """测试基础秒数格式转换"""
        # 1-2位数字作为秒数
        assert self.excel_handler.parse_shorthand_time("30") == "00:30"
        assert self.excel_handler.parse_shorthand_time("5") == "00:05"
        assert self.excel_handler.parse_shorthand_time("59") == "00:59"
        assert self.excel_handler.parse_shorthand_time("0") == "00:00"

    def test_parse_shorthand_time_minutes_seconds(self):
        """测试分钟秒数格式转换"""
        # 3位数字：第1位作为分钟，后2位作为秒数
        assert self.excel_handler.parse_shorthand_time("130") == "01:30"
        assert self.excel_handler.parse_shorthand_time("200") == "02:00"
        assert self.excel_handler.parse_shorthand_time("530") == "05:30"
        assert self.excel_handler.parse_shorthand_time("945") == "09:45"

    def test_parse_shorthand_time_long_format(self):
        """测试长格式转换"""
        # 4位及以上数字
        assert self.excel_handler.parse_shorthand_time("1200") == "12:00"
        assert self.excel_handler.parse_shorthand_time("1530") == "15:30"
        assert self.excel_handler.parse_shorthand_time("10045") == "100:45"

    def test_parse_shorthand_time_auto_carry(self):
        """测试自动进位功能"""
        # 秒数超过59时自动进位
        assert self.excel_handler.parse_shorthand_time("175") == "02:15"  # 1分75秒 → 2分15秒
        assert self.excel_handler.parse_shorthand_time("190") == "02:30"  # 1分90秒 → 2分30秒
        assert self.excel_handler.parse_shorthand_time("299") == "04:39"  # 2分99秒 → 4分39秒

    def test_parse_shorthand_time_standard_format(self):
        """测试标准MM:SS格式"""
        # 已经是标准格式的直接返回
        assert self.excel_handler.parse_shorthand_time("05:30") == "05:30"
        assert self.excel_handler.parse_shorthand_time("12:45") == "12:45"
        assert self.excel_handler.parse_shorthand_time("00:30") == "00:30"
        assert self.excel_handler.parse_shorthand_time("99:59") == "99:59"

    def test_parse_shorthand_time_edge_cases(self):
        """测试边界情况"""
        # 最小值
        assert self.excel_handler.parse_shorthand_time("0") == "00:00"
        # 最大合理值
        assert self.excel_handler.parse_shorthand_time("9959") == "99:59"

    def test_parse_shorthand_time_errors(self):
        """测试错误情况"""
        # 空输入
        with pytest.raises(ValueError, match="时间输入不能为空"):
            self.excel_handler.parse_shorthand_time("")

        with pytest.raises(ValueError, match="时间输入不能为空"):
            self.excel_handler.parse_shorthand_time(None)

        # 负数
        with pytest.raises(ValueError, match="时间不能为负数"):
            self.excel_handler.parse_shorthand_time("-30")

        # 非数字字符
        with pytest.raises(ValueError, match="时间格式错误，应为数字或MM:SS格式"):
            self.excel_handler.parse_shorthand_time("abc")

        # 标准格式错误
        with pytest.raises(ValueError, match="时间格式错误，应为MM:SS格式"):
            self.excel_handler.parse_shorthand_time("5:30:45")

        # 分钟或秒数超出范围
        with pytest.raises(ValueError, match="分钟数应在0-99范围内"):
            self.excel_handler.parse_shorthand_time("100:30")

        with pytest.raises(ValueError, match="秒数应在0-59范围内"):
            self.excel_handler.parse_shorthand_time("05:60")

        # 时间过长
        with pytest.raises(ValueError, match="时间过长，分钟数不能超过99"):
            self.excel_handler.parse_shorthand_time("10000")

    def test_parse_shorthand_time_with_spaces(self):
        """测试包含空格的输入"""
        assert self.excel_handler.parse_shorthand_time(" 130 ") == "01:30"
        assert self.excel_handler.parse_shorthand_time(" 05:30 ") == "05:30"
    
    def create_test_excel(self, data_rows=None):
        """创建测试用的Excel文件"""
        workbook = Workbook()
        worksheet = workbook.active
        
        # 写入表头
        headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "参与人数", "队伍信息"]
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)
        
        # 写入测试数据
        if data_rows is None:
            data_rows = [
                [1, 5, "05:30", 5, "队伍A,队伍B,队伍C,队伍D,队伍E"],
                [6, 10, "06:15", 5, "队伍F,队伍G,队伍H,队伍I,队伍J"],
                [11, 15, "07:00", 5, "队伍K,队伍L,队伍M,队伍N,队伍O"]
            ]
        
        for row_idx, row_data in enumerate(data_rows, 2):
            for col_idx, value in enumerate(row_data, 1):
                worksheet.cell(row=row_idx, column=col_idx, value=value)
        
        # 保存到字节流
        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()
        
        return excel_buffer
    
    def create_upload_file(self, excel_buffer, filename="test.xlsx"):
        """创建UploadFile对象"""
        return UploadFile(
            filename=filename,
            file=excel_buffer,
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    
    @pytest.mark.asyncio
    async def test_validate_excel_file_success(self):
        """测试Excel文件验证成功"""
        excel_buffer = self.create_test_excel()
        upload_file = self.create_upload_file(excel_buffer)
        
        result = await self.excel_handler.validate_excel_file(upload_file)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_excel_file_invalid_extension(self):
        """测试无效文件扩展名"""
        excel_buffer = self.create_test_excel()
        upload_file = self.create_upload_file(excel_buffer, "test.txt")
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.validate_excel_file(upload_file)
        
        assert "不支持的文件格式" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_validate_excel_file_too_large(self):
        """测试文件过大"""
        # 创建一个超大的Excel文件（模拟）
        large_content = b"x" * (11 * 1024 * 1024)  # 11MB
        upload_file = UploadFile(
            filename="large.xlsx",
            file=BytesIO(large_content),
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.validate_excel_file(upload_file)
        
        assert "文件大小超过限制" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_success(self):
        """测试Excel解析成功"""
        excel_buffer = self.create_test_excel()
        upload_file = self.create_upload_file(excel_buffer)
        
        result = await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert len(result) == 3
        
        # 检查第一条记录
        first_record = result[0]
        assert first_record['rank_start'] == 1
        assert first_record['rank_end'] == 5
        assert first_record['participant_count'] == 5
        assert first_record['completion_seconds'] == 5 * 60 + 30  # 5分30秒
        assert first_record['team_info'] == "队伍A,队伍B,队伍C,队伍D,队伍E"

    @pytest.mark.asyncio
    async def test_parse_ranking_excel_with_shorthand_time(self):
        """测试Excel解析简写时间格式"""
        # 创建包含简写时间格式的测试数据
        data_rows = [
            [1, 5, "330", 5, "队伍A,队伍B,队伍C,队伍D,队伍E"],  # 330 → 05:30
            [6, 10, "200", 5, "队伍F,队伍G,队伍H,队伍I,队伍J"],  # 200 → 02:00
            [11, 15, 30, 5, "队伍K,队伍L,队伍M,队伍N,队伍O"],    # 30 → 00:30
            [16, 20, "1200", 5, "队伍P,队伍Q,队伍R,队伍S,队伍T"], # 1200 → 12:00
            [21, 25, "175", 5, "队伍U,队伍V,队伍W,队伍X,队伍Y"]   # 175 → 02:15 (自动进位)
        ]

        excel_buffer = self.create_test_excel(data_rows)
        upload_file = self.create_upload_file(excel_buffer)

        result = await self.excel_handler.parse_ranking_excel(upload_file)

        assert len(result) == 5

        # 验证简写格式转换结果
        assert result[0]['completion_seconds'] == 5 * 60 + 30  # 330 → 05:30
        assert result[1]['completion_seconds'] == 2 * 60       # 200 → 02:00
        assert result[2]['completion_seconds'] == 30           # 30 → 00:30
        assert result[3]['completion_seconds'] == 12 * 60      # 1200 → 12:00
        assert result[4]['completion_seconds'] == 2 * 60 + 15  # 175 → 02:15

        # 验证time对象
        assert result[0]['completion_time'] == time(minute=5, second=30)
        assert result[1]['completion_time'] == time(minute=2, second=0)
        assert result[2]['completion_time'] == time(minute=0, second=30)
        assert result[3]['completion_time'] == time(minute=12, second=0)
        assert result[4]['completion_time'] == time(minute=2, second=15)

    @pytest.mark.asyncio
    async def test_parse_ranking_excel_mixed_time_formats(self):
        """测试Excel解析混合时间格式"""
        # 创建包含标准格式和简写格式混合的测试数据
        data_rows = [
            [1, 5, "05:30", 5, "队伍A"],    # 标准格式
            [6, 10, "330", 5, "队伍B"],     # 简写格式
            [11, 15, "02:15", 5, "队伍C"],  # 标准格式
            [16, 20, 45, 5, "队伍D"]        # 简写格式（数字类型）
        ]

        excel_buffer = self.create_test_excel(data_rows)
        upload_file = self.create_upload_file(excel_buffer)

        result = await self.excel_handler.parse_ranking_excel(upload_file)

        assert len(result) == 4

        # 验证混合格式都能正确解析
        assert result[0]['completion_seconds'] == 5 * 60 + 30  # 05:30
        assert result[1]['completion_seconds'] == 5 * 60 + 30  # 330 → 05:30
        assert result[2]['completion_seconds'] == 2 * 60 + 15  # 02:15
        assert result[3]['completion_seconds'] == 45           # 45 → 00:45

    @pytest.mark.asyncio
    async def test_parse_ranking_excel_invalid_shorthand_time(self):
        """测试Excel解析无效简写时间格式"""
        # 创建包含无效时间格式的测试数据
        data_rows = [
            [1, 5, "abc", 5, "队伍A"],      # 无效格式
            [6, 10, "10000", 5, "队伍B"]    # 时间过长
        ]

        excel_buffer = self.create_test_excel(data_rows)
        upload_file = self.create_upload_file(excel_buffer)

        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.parse_ranking_excel(upload_file)

        error_message = str(exc_info.value)
        assert "完成时间格式错误" in error_message

    @pytest.mark.asyncio
    async def test_parse_ranking_excel_missing_required_columns(self):
        """测试缺少必需列"""
        workbook = Workbook()
        worksheet = workbook.active
        
        # 只写入部分表头
        headers = ["排名开始", "参与人数"]  # 缺少其他必需列
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)
        
        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()
        
        upload_file = self.create_upload_file(excel_buffer)
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert "缺少必需列" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_invalid_data_types(self):
        """测试无效数据类型"""
        data_rows = [
            ["abc", 5, "05:30", 5, "队伍A"],  # 排名开始应为数字
            [6, "def", "06:15", 5, "队伍B"],  # 排名结束应为数字
            [11, 15, "invalid", 5, "队伍C"],  # 时间格式错误
            [16, 20, "07:00", "xyz", "队伍D"]  # 参与人数应为数字
        ]
        
        excel_buffer = self.create_test_excel(data_rows)
        upload_file = self.create_upload_file(excel_buffer)
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert "数据解析错误" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_invalid_rank_range(self):
        """测试无效排名范围"""
        data_rows = [
            [10, 5, "05:30", 5, "队伍A"]  # 排名开始大于排名结束
        ]
        
        excel_buffer = self.create_test_excel(data_rows)
        upload_file = self.create_upload_file(excel_buffer)
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert "排名开始不能大于排名结束" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_empty_required_fields(self):
        """测试必需字段为空"""
        data_rows = [
            [None, 5, "05:30", 5, "队伍A"],  # 排名开始为空
            [6, 10, None, 5, "队伍B"],       # 完成时间为空
            [11, 15, "07:00", None, "队伍C"] # 参与人数为空
        ]
        
        excel_buffer = self.create_test_excel(data_rows)
        upload_file = self.create_upload_file(excel_buffer)
        
        with pytest.raises(Exception) as exc_info:
            await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert "不能为空" in str(exc_info.value)
    
    def test_create_ranking_template_success(self):
        """测试创建Excel模板成功"""
        excel_buffer = self.excel_handler.create_ranking_template("5_person")
        
        assert excel_buffer is not None
        assert len(excel_buffer.getvalue()) > 0
        
        # 验证可以正常读取
        from openpyxl import load_workbook
        workbook = load_workbook(excel_buffer)
        
        # 检查工作表
        assert "榜单明细模板" in workbook.sheetnames
        assert "使用说明" in workbook.sheetnames
        
        # 检查表头
        worksheet = workbook["榜单明细模板"]
        expected_headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "参与人数", "队伍信息"]
        
        for col_idx, expected_header in enumerate(expected_headers, 1):
            actual_header = worksheet.cell(row=1, column=col_idx).value
            assert expected_header in actual_header
        
        workbook.close()
    
    def test_create_ranking_template_different_types(self):
        """测试创建不同类型的榜单模板"""
        for ranking_type in ["5_person", "10_person"]:
            excel_buffer = self.excel_handler.create_ranking_template(ranking_type)
            assert excel_buffer is not None
            assert len(excel_buffer.getvalue()) > 0
    
    @pytest.mark.asyncio
    async def test_parse_ranking_excel_with_time_objects(self):
        """测试解析包含时间对象的Excel"""
        workbook = Workbook()
        worksheet = workbook.active
        
        # 写入表头
        headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "参与人数", "队伍信息"]
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)
        
        # 写入包含时间对象的数据
        worksheet.cell(row=2, column=1, value=1)
        worksheet.cell(row=2, column=2, value=5)
        worksheet.cell(row=2, column=3, value=time(minute=5, second=30))  # 时间对象
        worksheet.cell(row=2, column=4, value=5)
        worksheet.cell(row=2, column=5, value="队伍A")
        
        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()
        
        upload_file = self.create_upload_file(excel_buffer)
        result = await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert len(result) == 1
        assert result[0]['completion_seconds'] == 5 * 60 + 30

    @pytest.mark.asyncio
    async def test_parse_ranking_excel_time_object_with_hour(self):
        """测试Excel中时间对象包含小时的情况"""
        workbook = Workbook()
        worksheet = workbook.active

        # 写入表头
        headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "队伍信息"]
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)

        # 写入包含小时的时间对象（模拟Excel将01:44解析为01:44:00的情况）
        worksheet.cell(row=2, column=1, value=1)
        worksheet.cell(row=2, column=2, value=5)
        worksheet.cell(row=2, column=3, value=time(hour=1, minute=44, second=0))  # 1:44:00 应该被解析为 104分钟
        worksheet.cell(row=2, column=4, value="队伍A")

        # 写入另一个测试用例
        worksheet.cell(row=3, column=1, value=6)
        worksheet.cell(row=3, column=2, value=10)
        worksheet.cell(row=3, column=3, value=time(hour=0, minute=5, second=30))  # 0:05:30 应该被解析为 5分30秒
        worksheet.cell(row=3, column=4, value="队伍B")

        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()

        upload_file = self.create_upload_file(excel_buffer)
        result = await self.excel_handler.parse_ranking_excel(upload_file)

        assert len(result) == 2

        # 验证第一条记录：1:44:00 -> 104分钟
        assert result[0]['completion_seconds'] == 104 * 60  # 1小时44分钟 = 104分钟
        assert result[0]['completion_time'] == time(minute=104 % 60, second=0)  # 但time对象只能存储0-59分钟

        # 验证第二条记录：0:05:30 -> 5分30秒
        assert result[1]['completion_seconds'] == 5 * 60 + 30
        assert result[1]['completion_time'] == time(minute=5, second=30)

    @pytest.mark.asyncio
    async def test_parse_ranking_excel_skip_empty_rows(self):
        """测试跳过空行"""
        workbook = Workbook()
        worksheet = workbook.active
        
        # 写入表头
        headers = ["排名开始", "排名结束", "完成时间(MM:SS)", "参与人数", "队伍信息"]
        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)
        
        # 写入数据，中间包含空行
        worksheet.cell(row=2, column=1, value=1)
        worksheet.cell(row=2, column=2, value=5)
        worksheet.cell(row=2, column=3, value="05:30")
        worksheet.cell(row=2, column=4, value=5)
        worksheet.cell(row=2, column=5, value="队伍A")
        
        # 第3行为空行
        
        worksheet.cell(row=4, column=1, value=6)
        worksheet.cell(row=4, column=2, value=10)
        worksheet.cell(row=4, column=3, value="06:15")
        worksheet.cell(row=4, column=4, value=5)
        worksheet.cell(row=4, column=5, value="队伍B")
        
        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        workbook.close()
        
        upload_file = self.create_upload_file(excel_buffer)
        result = await self.excel_handler.parse_ranking_excel(upload_file)
        
        assert len(result) == 2  # 应该跳过空行，只解析2条记录
