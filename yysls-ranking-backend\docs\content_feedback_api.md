# 内容管理与用户反馈API接口文档

## 概述

本文档描述了燕友圈榜单系统中内容管理和用户反馈相关的API接口。

## 基础信息

- **基础URL**: `http://localhost:8000/api/v1`
- **认证方式**: <PERSON><PERSON>
- **响应格式**: JSON

## 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

## 内容管理接口

### 1. 获取内容列表

**接口地址**: `GET /content/contents`

**权限要求**: 管理员权限

**请求参数**:
- `page` (int, 可选): 页码，默认1
- `size` (int, 可选): 每页大小，默认10，最大100
- `content_type` (string, 可选): 内容类型筛选
- `is_published` (boolean, 可选): 发布状态筛选
- `search` (string, 可选): 搜索关键词

**响应示例**:
```json
{
  "code": 200,
  "message": "获取内容列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "content_type": "announcement",
        "title": "系统公告",
        "content": "公告内容",
        "is_published": true,
        "publish_at": "2024-01-01T00:00:00",
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

### 2. 创建内容

**接口地址**: `POST /content/contents`

**权限要求**: 管理员权限

**请求体**:
```json
{
  "content_type": "announcement",
  "title": "新公告标题",
  "content": "公告内容",
  "is_published": false,
  "publish_at": "2024-01-01T00:00:00"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "创建内容成功",
  "data": {
    "id": 1,
    "content_type": "announcement",
    "title": "新公告标题",
    "content": "公告内容",
    "is_published": false,
    "publish_at": "2024-01-01T00:00:00",
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T00:00:00"
  }
}
```

### 3. 获取内容详情

**接口地址**: `GET /content/contents/{content_id}`

**权限要求**: 无

**路径参数**:
- `content_id` (int): 内容ID

**响应示例**:
```json
{
  "code": 200,
  "message": "获取内容详情成功",
  "data": {
    "id": 1,
    "content_type": "announcement",
    "title": "公告标题",
    "content": "公告内容",
    "is_published": true,
    "publish_at": "2024-01-01T00:00:00",
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T00:00:00"
  }
}
```

### 4. 更新内容

**接口地址**: `PUT /content/contents`

**权限要求**: 管理员权限

**请求体**:
```json
{
  "id": 1,
  "content_type": "announcement",
  "title": "更新后的标题",
  "content": "更新后的内容",
  "is_published": true,
  "publish_at": "2024-01-01T00:00:00"
}
```

### 5. 删除内容

**接口地址**: `DELETE /content/contents/{content_id}`

**权限要求**: 管理员权限

**路径参数**:
- `content_id` (int): 内容ID

### 6. 获取公告列表（公开接口）

**接口地址**: `GET /content/public/announcements`

**权限要求**: 无

**请求参数**:
- `limit` (int, 可选): 获取数量限制，默认10，最大50

### 7. 获取关于我们内容（公开接口）

**接口地址**: `GET /content/public/about`

**权限要求**: 无

### 8. 获取播报消息列表

**接口地址**: `GET /content/broadcast-messages`

**权限要求**: 无

**请求参数**:
- `limit` (int, 可选): 获取数量限制，默认10，最大50
- `is_active` (boolean, 可选): 激活状态筛选

### 9. 创建播报消息

**接口地址**: `POST /content/broadcast-messages`

**权限要求**: 管理员权限

**请求体**:
```json
{
  "message": "播报消息内容",
  "message_type": "info",
  "is_active": true,
  "start_time": "2024-01-01T00:00:00",
  "end_time": "2024-01-02T00:00:00",
  "display_duration": 5,
  "display_order": 1
}
```

## 用户反馈接口

### 1. 提交反馈

**接口地址**: `POST /feedback`

**权限要求**: 无（支持匿名反馈）

**请求体**:
```json
{
  "content": "这是用户反馈内容",
  "user_id": null
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "反馈提交成功",
  "data": {
    "id": 1,
    "content": "这是用户反馈内容",
    "user_id": 1,
    "status": "pending",
    "admin_reply": null,
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T00:00:00",
    "user_nickname": "用户昵称",
    "user_username": "username",
    "avatar_url": "http://localhost:8000/uploads/images/avatars/avatar_20240115_103000_abc12345_1001.jpg"
  }
}
```

### 2. 获取我的反馈列表

**接口地址**: `GET /feedback/my`

**权限要求**: 用户登录

**请求参数**:
- `page` (int, 可选): 页码，默认1
- `page_size` (int, 可选): 每页数量，默认20，最大100

**响应示例**:
```json
{
  "code": 200,
  "message": "获取反馈列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "content": "我的反馈内容",
        "user_id": 1,
        "status": "pending",
        "admin_reply": null,
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00",
        "user_nickname": "我的昵称",
        "user_username": "myusername",
        "avatar_url": "http://localhost:8000/uploads/images/avatars/avatar_20240115_103000_abc12345_1001.jpg"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20,
    "total_pages": 1
  }
}
```

### 3. 管理员获取反馈列表

**接口地址**: `GET /feedback/admin/list`

**权限要求**: 管理员权限

**请求参数**:
- `status` (string, 可选): 按状态筛选（pending/processing/resolved/ignored）
- `user_id` (int, 可选): 按用户ID筛选
- `page` (int, 可选): 页码，默认1
- `page_size` (int, 可选): 每页数量，默认20，最大100
- `order_by` (string, 可选): 排序字段，默认created_at
- `order_desc` (boolean, 可选): 是否降序，默认true

### 4. 管理员获取反馈详情

**接口地址**: `GET /feedback/admin/{feedback_id}`

**权限要求**: 管理员权限

**路径参数**:
- `feedback_id` (int): 反馈ID

### 5. 管理员更新反馈

**接口地址**: `PUT /feedback/admin/{feedback_id}`

**权限要求**: 管理员权限

**路径参数**:
- `feedback_id` (int): 反馈ID

**请求体**:
```json
{
  "status": "resolved",
  "admin_reply": "问题已解决，感谢您的反馈"
}
```

### 6. 管理员获取反馈统计

**接口地址**: `GET /feedback/admin/stats`

**权限要求**: 管理员权限

**响应示例**:
```json
{
  "code": 200,
  "message": "获取反馈统计成功",
  "data": {
    "total_count": 100,
    "pending_count": 20,
    "processing_count": 10,
    "resolved_count": 65,
    "ignored_count": 5,
    "today_count": 8,
    "week_count": 25
  }
}
```

## 反馈状态说明

- `pending`: 待处理
- `processing`: 处理中
- `resolved`: 已处理
- `ignored`: 已忽略

## 错误码说明

- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权（需要登录）
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 数据验证失败
- `500`: 服务器内部错误

## 使用示例

### 提交匿名反馈

```bash
curl -X POST "http://localhost:8000/api/v1/feedback" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "系统运行很流畅，但希望能增加更多功能",
    "user_id": null
  }'
```

### 管理员查看反馈统计

```bash
curl -X GET "http://localhost:8000/api/v1/feedback/admin/stats" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### 获取公告列表

```bash
curl -X GET "http://localhost:8000/api/v1/content/public/announcements?limit=5"
```
