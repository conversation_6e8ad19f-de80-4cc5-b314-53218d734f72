#!/usr/bin/env python3
"""
测试静态文件服务
"""
import requests
import os
from app.config import settings

def test_static_file_service():
    """测试静态文件服务"""
    print("=== 静态文件服务测试 ===")
    
    # 测试基础URL
    base_url = "https://yysls.sappan.top"  # 你的实际域名
    
    # 测试上传目录访问
    uploads_test_url = f"{base_url}/uploads/"
    print(f"测试上传目录访问: {uploads_test_url}")
    
    try:
        response = requests.get(uploads_test_url, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text[:200]}...")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    
    # 测试具体的图片文件
    image_url = "https://yysls.sappan.top/uploads/images/temp/temp_20250805_000519_138ca98b_1.jpg"
    print(f"\n测试具体图片文件: {image_url}")
    
    try:
        response = requests.get(image_url, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        if response.status_code == 200:
            print(f"文件大小: {len(response.content)} bytes")
            print("✓ 图片文件访问成功")
        else:
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    
    # 检查本地文件是否存在
    print(f"\n=== 本地文件检查 ===")
    upload_dir = settings.upload_dir_absolute
    print(f"上传目录: {upload_dir}")
    print(f"上传目录存在: {os.path.exists(upload_dir)}")
    
    image_path = os.path.join(upload_dir, "images", "temp", "temp_20250805_000519_138ca98b_1.jpg")
    print(f"图片文件路径: {image_path}")
    print(f"图片文件存在: {os.path.exists(image_path)}")
    
    if os.path.exists(image_path):
        file_size = os.path.getsize(image_path)
        print(f"文件大小: {file_size} bytes")
    
    # 列出temp目录下的文件
    temp_dir = os.path.join(upload_dir, "images", "temp")
    if os.path.exists(temp_dir):
        files = os.listdir(temp_dir)
        print(f"temp目录文件列表: {files}")
    
    print(f"\n=== 建议检查 ===")
    print("1. 确认服务器已重启，静态文件服务已生效")
    print("2. 检查Nginx配置是否正确代理静态文件")
    print("3. 检查服务器防火墙设置")
    print("4. 查看服务器日志中的静态文件服务启动信息")

if __name__ == "__main__":
    test_static_file_service()
