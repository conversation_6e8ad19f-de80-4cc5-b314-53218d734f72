"""
赞助商管理API端点

提供赞助商的创建、查询、更新、删除等功能
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.sponsor_service import sponsor_service
from app.services.user_service import UserService
from app.schemas.sponsor import SponsorCreate, SponsorUpdate, SponsorResponse
from app.schemas.common import ResponseModel, PaginatedResponse
from app.utils.security import verify_token
from app.models.user import UserRole

router = APIRouter()
security = HTTPBearer()
user_service = UserService()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """获取当前用户对象"""
    payload = verify_token(credentials.credentials)
    user_id = int(payload.get("sub"))
    
    user = user_service.get(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


@router.get("", response_model=ResponseModel[PaginatedResponse[SponsorResponse]], summary="获取赞助商列表")
async def get_sponsors(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词（名称）"),
    is_active: Optional[bool] = Query(None, description="激活状态筛选"),
    db: Session = Depends(get_db)
) -> ResponseModel[PaginatedResponse[SponsorResponse]]:
    """
    获取赞助商列表

    - **page**: 页码，从1开始
    - **size**: 每页大小，最大100
    - **search**: 搜索关键词（可选）
    - **is_active**: 激活状态筛选（可选）
    """
    try:
        # 获取赞助商列表
        if search:
            sponsors, total = sponsor_service.search_sponsors(
                db, search_term=search, skip=(page - 1) * size, limit=size, is_active=is_active
            )
        else:
            sponsors = sponsor_service.get_all(
                db, skip=(page - 1) * size, limit=size, is_active=is_active
            )
            # 获取总数
            all_sponsors = sponsor_service.get_all(db, is_active=is_active)
            total = len(all_sponsors)

        # 转换为响应模型
        sponsor_responses = [SponsorResponse.model_validate(sponsor) for sponsor in sponsors]

        # 构建分页响应
        pages = (total + size - 1) // size
        paginated_data = PaginatedResponse(
            items=sponsor_responses,
            total=total,
            page=page,
            size=size,
            pages=pages
        )

        return ResponseModel(
            code=200,
            message="获取赞助商列表成功",
            data=paginated_data
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取赞助商列表失败: {str(e)}"
        )


@router.get("/active", response_model=ResponseModel[List[SponsorResponse]], summary="获取活跃赞助商列表")
async def get_active_sponsors(
    db: Session = Depends(get_db)
) -> ResponseModel[List[SponsorResponse]]:
    """
    获取活跃赞助商列表（按排序顺序排序）

    用于前端展示
    """
    try:
        sponsors = sponsor_service.get_active_sponsors(db)
        sponsor_responses = [SponsorResponse.model_validate(sponsor) for sponsor in sponsors]

        return ResponseModel(
            code=200,
            message="获取活跃赞助商列表成功",
            data=sponsor_responses
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取活跃赞助商列表失败: {str(e)}"
        )


@router.post("", response_model=ResponseModel[SponsorResponse], summary="创建赞助商")
async def create_sponsor(
    sponsor_data: SponsorCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[SponsorResponse]:
    """
    创建新赞助商（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 创建赞助商
        sponsor = sponsor_service.create(db, obj_in=sponsor_data)
        
        return ResponseModel(
            code=200,
            message="创建赞助商成功",
            data=SponsorResponse.model_validate(sponsor)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建赞助商失败: {str(e)}"
        )


@router.get("/{sponsor_id}", response_model=ResponseModel[SponsorResponse], summary="获取赞助商详情")
async def get_sponsor(
    sponsor_id: int,
    db: Session = Depends(get_db)
) -> ResponseModel[SponsorResponse]:
    """
    获取指定赞助商的详细信息
    """
    try:
        sponsor = sponsor_service.get(db, sponsor_id)
        if not sponsor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="赞助商不存在"
            )
        
        return ResponseModel(
            code=200,
            message="获取赞助商详情成功",
            data=SponsorResponse.model_validate(sponsor)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取赞助商详情失败: {str(e)}"
        )


@router.put("", response_model=ResponseModel[SponsorResponse], summary="更新赞助商信息")
async def update_sponsor(
    sponsor_data: SponsorUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[SponsorResponse]:
    """
    更新赞助商信息（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 检查赞助商是否存在
        existing_sponsor = sponsor_service.get(db, sponsor_data.id)
        if not existing_sponsor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="赞助商不存在"
            )
        
        # 更新赞助商信息
        sponsor = sponsor_service.update(db, db_obj=existing_sponsor, obj_in=sponsor_data)
        
        return ResponseModel(
            code=200,
            message="更新赞助商信息成功",
            data=SponsorResponse.model_validate(sponsor)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新赞助商信息失败: {str(e)}"
        )


@router.delete("/{sponsor_id}", response_model=ResponseModel[None], summary="删除赞助商")
async def delete_sponsor(
    sponsor_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[None]:
    """
    删除赞助商（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 检查赞助商是否存在
        sponsor = sponsor_service.get(db, sponsor_id)
        if not sponsor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="赞助商不存在"
            )
        
        # 删除赞助商
        sponsor_service.remove(db, id=sponsor_id)
        
        return ResponseModel(
            code=200,
            message="删除赞助商成功",
            data=None
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除赞助商失败: {str(e)}"
        )


@router.put("/{sponsor_id}/sort-order", response_model=ResponseModel[SponsorResponse], summary="更新赞助商排序")
async def update_sponsor_sort_order(
    sponsor_id: int,
    sort_order: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[SponsorResponse]:
    """
    更新赞助商排序顺序（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    try:
        # 检查赞助商是否存在
        sponsor = sponsor_service.get(db, sponsor_id)
        if not sponsor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="赞助商不存在"
            )

        # 更新排序顺序
        updated_sponsor = sponsor_service.update_sort_order(db, sponsor_id, sort_order)

        return ResponseModel(
            code=200,
            message="更新赞助商排序成功",
            data=SponsorResponse.model_validate(updated_sponsor)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新赞助商排序失败: {str(e)}"
        )


@router.put("/{sponsor_id}/toggle-status", response_model=ResponseModel[SponsorResponse], summary="切换赞助商激活状态")
async def toggle_sponsor_status(
    sponsor_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[SponsorResponse]:
    """
    切换赞助商的激活状态（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    try:
        # 检查赞助商是否存在
        sponsor = sponsor_service.get(db, sponsor_id)
        if not sponsor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="赞助商不存在"
            )

        # 切换激活状态
        updated_sponsor = sponsor_service.toggle_active_status(db, sponsor_id)

        return ResponseModel(
            code=200,
            message="切换赞助商激活状态成功",
            data=SponsorResponse.model_validate(updated_sponsor)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"切换赞助商激活状态失败: {str(e)}"
        )



