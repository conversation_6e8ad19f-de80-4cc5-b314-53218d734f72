"""
用户反馈API接口

提供用户反馈的创建、查询、管理等功能
"""
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import (
    get_current_user, get_optional_current_user, require_admin
)
from app.models.user import User
from app.models.feedback import FeedbackStatus
from app.services.feedback_service import FeedbackService
from app.schemas.feedback import (
    FeedbackCreate, FeedbackUpdate, FeedbackResponse, 
    FeedbackListQuery, FeedbackListResponse, FeedbackStatsResponse
)
from app.schemas.common import ResponseModel, PaginatedResponse
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
feedback_service = FeedbackService()


@router.post("", response_model=ResponseModel[FeedbackResponse])
async def create_feedback(
    feedback_data: FeedbackCreate,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """
    提交反馈
    
    - 支持登录用户和匿名用户提交反馈
    - 登录用户的反馈会自动关联用户信息
    - 匿名反馈不会记录用户信息
    """
    try:
        # 获取当前用户ID（如果已登录）
        current_user_id = current_user.id if current_user else None
        
        # 创建反馈
        feedback = feedback_service.create_feedback(
            db=db, 
            feedback_data=feedback_data,
            current_user_id=current_user_id
        )
        
        # 构建响应数据
        response_data = FeedbackResponse(
            id=feedback.id,
            content=feedback.content,
            user_id=feedback.user_id,
            status=FeedbackStatus(feedback.status),
            admin_reply=feedback.admin_reply,
            created_at=feedback.created_at,
            updated_at=feedback.updated_at,
            user_nickname=current_user.nickname if current_user else None,
            user_username=current_user.username if current_user else None,
            avatar_url=current_user.avatar_url if current_user else None
        )
        
        return ResponseModel(
            code=200,
            message="反馈提交成功",
            data=response_data
        )
        
    except Exception as e:
        logger.error(f"提交反馈失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交反馈失败"
        )


@router.get("/my", response_model=ResponseModel[FeedbackListResponse])
async def get_my_feedbacks(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取我的反馈列表
    
    - 需要用户登录
    - 只能查看自己提交的反馈
    """
    try:
        feedbacks, total = feedback_service.get_user_feedbacks(
            db=db,
            user_id=current_user.id,
            page=page,
            page_size=page_size
        )
        
        # 构建响应数据
        feedback_responses = []
        for feedback in feedbacks:
            feedback_responses.append(FeedbackResponse(
                id=feedback.id,
                content=feedback.content,
                user_id=feedback.user_id,
                status=FeedbackStatus(feedback.status),
                admin_reply=feedback.admin_reply,
                created_at=feedback.created_at,
                updated_at=feedback.updated_at,
                user_nickname=current_user.nickname,
                user_username=current_user.username,
                avatar_url=current_user.avatar_url
            ))
        
        total_pages = (total + page_size - 1) // page_size
        
        response_data = FeedbackListResponse(
            items=feedback_responses,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
        return ResponseModel(
            code=200,
            message="获取反馈列表成功",
            data=response_data
        )
        
    except Exception as e:
        logger.error(f"获取用户反馈列表失败 user_id={current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取反馈列表失败"
        )


@router.get("/admin/list", response_model=ResponseModel[FeedbackListResponse])
async def get_admin_feedback_list(
    status: Optional[FeedbackStatus] = Query(None, description="按状态筛选"),
    user_id: Optional[int] = Query(None, description="按用户ID筛选"),
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    order_by: str = Query(default="created_at", description="排序字段"),
    order_desc: bool = Query(default=True, description="是否降序"),
    db: Session = Depends(get_db),
    admin_user: User = Depends(require_admin)
):
    """
    管理员获取反馈列表
    
    - 需要管理员权限
    - 支持按状态、用户ID筛选
    - 支持分页和排序
    """
    try:
        query_params = FeedbackListQuery(
            status=status,
            user_id=user_id,
            page=page,
            page_size=page_size,
            order_by=order_by,
            order_desc=order_desc
        )
        
        feedbacks, total = feedback_service.get_feedback_list(
            db=db,
            query_params=query_params,
            include_user_info=True
        )
        
        # 构建响应数据
        feedback_responses = []
        for feedback in feedbacks:
            # 获取用户信息
            user_nickname = None
            user_username = None
            avatar_url = None
            if feedback.user:
                user_nickname = feedback.user.nickname
                user_username = feedback.user.username
                avatar_url = feedback.user.avatar_url

            feedback_responses.append(FeedbackResponse(
                id=feedback.id,
                content=feedback.content,
                user_id=feedback.user_id,
                status=FeedbackStatus(feedback.status),
                admin_reply=feedback.admin_reply,
                created_at=feedback.created_at,
                updated_at=feedback.updated_at,
                user_nickname=user_nickname,
                user_username=user_username,
                avatar_url=avatar_url
            ))
        
        total_pages = (total + page_size - 1) // page_size
        
        response_data = FeedbackListResponse(
            items=feedback_responses,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
        return ResponseModel(
            code=200,
            message="获取反馈列表成功",
            data=response_data
        )
        
    except Exception as e:
        logger.error(f"管理员获取反馈列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取反馈列表失败"
        )


@router.get("/admin/{feedback_id}", response_model=ResponseModel[FeedbackResponse])
async def get_feedback_detail(
    feedback_id: int,
    db: Session = Depends(get_db),
    admin_user: User = Depends(require_admin)
):
    """
    管理员获取单个反馈详情

    - 需要管理员权限
    """
    try:
        feedback = feedback_service.get(db, feedback_id)
        if not feedback:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="反馈不存在"
            )

        # 获取用户信息
        user_nickname = None
        user_username = None
        avatar_url = None
        if feedback.user:
            user_nickname = feedback.user.nickname
            user_username = feedback.user.username
            avatar_url = feedback.user.avatar_url

        response_data = FeedbackResponse(
            id=feedback.id,
            content=feedback.content,
            user_id=feedback.user_id,
            status=FeedbackStatus(feedback.status),
            admin_reply=feedback.admin_reply,
            created_at=feedback.created_at,
            updated_at=feedback.updated_at,
            user_nickname=user_nickname,
            user_username=user_username,
            avatar_url=avatar_url
        )

        return ResponseModel(
            code=200,
            message="获取反馈详情成功",
            data=response_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取反馈详情失败 ID={feedback_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取反馈详情失败"
        )


@router.put("/admin/{feedback_id}", response_model=ResponseModel[FeedbackResponse])
async def update_feedback(
    feedback_id: int,
    update_data: FeedbackUpdate,
    db: Session = Depends(get_db),
    admin_user: User = Depends(require_admin)
):
    """
    管理员更新反馈状态或添加回复

    - 需要管理员权限
    - 可以更新反馈状态
    - 可以添加管理员回复
    """
    try:
        feedback = feedback_service.update_feedback(
            db=db,
            feedback_id=feedback_id,
            update_data=update_data
        )

        if not feedback:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="反馈不存在"
            )

        # 获取用户信息
        user_nickname = None
        user_username = None
        if feedback.user:
            user_nickname = feedback.user.nickname
            user_username = feedback.user.username

        response_data = FeedbackResponse(
            id=feedback.id,
            content=feedback.content,
            user_id=feedback.user_id,
            status=FeedbackStatus(feedback.status),
            admin_reply=feedback.admin_reply,
            created_at=feedback.created_at,
            updated_at=feedback.updated_at,
            user_nickname=user_nickname,
            user_username=user_username
        )

        return ResponseModel(
            code=200,
            message="更新反馈成功",
            data=response_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新反馈失败 ID={feedback_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新反馈失败"
        )


@router.get("/admin/stats", response_model=ResponseModel[FeedbackStatsResponse])
async def get_feedback_stats(
    db: Session = Depends(get_db),
    admin_user: User = Depends(require_admin)
):
    """
    管理员获取反馈统计信息

    - 需要管理员权限
    - 返回各种状态的反馈数量统计
    """
    try:
        stats = feedback_service.get_feedback_stats(db)

        return ResponseModel(
            code=200,
            message="获取反馈统计成功",
            data=stats
        )

    except Exception as e:
        logger.error(f"获取反馈统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取反馈统计失败"
        )
