"""
图片处理工具类

提供图片上传、压缩、缩略图生成等功能
"""
import os
import uuid
import logging
from datetime import datetime
from typing import Optional, Tuple, List, Dict, Any
from pathlib import Path

from PIL import Image, ImageOps
from fastapi import UploadFile, HTTPException, status

from app.config import settings


logger = logging.getLogger(__name__)


class ImageHandler:
    """图片处理工具类"""
    
    # 支持的图片格式
    ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.webp', '.gif'}
    ALLOWED_MIME_TYPES = {
        'image/jpeg', 'image/jpg', 'image/png', 
        'image/webp', 'image/gif'
    }
    
    # 图片大小限制
    MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
    MAX_DIMENSION = 2048  # 最大宽度或高度
    
    # 缩略图尺寸配置
    THUMBNAIL_SIZES = {
        'small': (150, 150),
        'medium': (300, 300),
        'large': (600, 600)
    }
    
    def __init__(self):
        """初始化图片处理器"""
        self.upload_dir = settings.upload_dir_absolute
        self.static_dir = settings.static_dir_absolute
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            os.path.join(self.upload_dir, "images"),
            os.path.join(self.upload_dir, "images", "sponsors"),
            os.path.join(self.upload_dir, "images", "avatars"),
            os.path.join(self.upload_dir, "images", "content"),
            os.path.join(self.upload_dir, "images", "temp"),
            os.path.join(self.upload_dir, "thumbnails"),
            os.path.join(self.upload_dir, "thumbnails", "sponsors"),
            os.path.join(self.upload_dir, "thumbnails", "avatars"),
            os.path.join(self.upload_dir, "thumbnails", "content"),
        ]
        
        for directory in directories:
            try:
                os.makedirs(directory, mode=0o755, exist_ok=True)
            except Exception as e:
                logger.error(f"创建目录失败: {directory}, 错误: {str(e)}")
    
    async def validate_image_file(self, file: UploadFile) -> None:
        """验证图片文件"""
        # 检查文件名
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件名不能为空"
            )
        
        # 检查文件扩展名
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in self.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件格式，支持的格式: {', '.join(self.ALLOWED_EXTENSIONS)}"
            )
        
        # 检查MIME类型
        if file.content_type not in self.ALLOWED_MIME_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件类型: {file.content_type}"
            )
        
        # 检查文件大小
        file_content = await file.read()
        await file.seek(0)  # 重置文件指针
        
        if len(file_content) > self.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"文件大小超过限制，最大允许 {self.MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        # 验证图片格式
        try:
            image = Image.open(file.file)
            image.verify()
            await file.seek(0)  # 重置文件指针
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的图片文件: {str(e)}"
            )
    
    def _generate_filename(self, category: str, user_id: int, original_filename: str) -> str:
        """生成唯一文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = uuid.uuid4().hex[:8]
        file_extension = os.path.splitext(original_filename)[1].lower()
        
        return f"{category}_{timestamp}_{unique_id}_{user_id}{file_extension}"
    
    def _optimize_image(self, image: Image.Image, max_dimension: int = None) -> Image.Image:
        """优化图片（压缩和调整大小）"""
        if max_dimension is None:
            max_dimension = self.MAX_DIMENSION
        
        # 转换RGBA到RGB（如果需要）
        if image.mode in ('RGBA', 'LA', 'P'):
            # 创建白色背景
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode in ('RGBA', 'LA') else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 调整图片大小（保持宽高比）
        if image.width > max_dimension or image.height > max_dimension:
            image.thumbnail((max_dimension, max_dimension), Image.Resampling.LANCZOS)
        
        return image
    
    def _create_thumbnail(self, image: Image.Image, size: Tuple[int, int]) -> Image.Image:
        """创建缩略图"""
        # 使用ImageOps.fit来创建居中裁剪的缩略图
        thumbnail = ImageOps.fit(image, size, Image.Resampling.LANCZOS)
        return thumbnail
    
    async def save_image(
        self, 
        file: UploadFile, 
        category: str, 
        user_id: int,
        create_thumbnails: bool = True
    ) -> Dict[str, Any]:
        """
        保存图片文件
        
        Args:
            file: 上传的文件
            category: 图片分类 (sponsors, avatars, content, temp)
            user_id: 用户ID
            create_thumbnails: 是否创建缩略图
            
        Returns:
            包含文件信息的字典
        """
        try:
            # 验证文件
            await self.validate_image_file(file)
            
            # 生成文件名
            filename = self._generate_filename(category, user_id, file.filename)
            
            # 确定保存路径
            image_dir = os.path.join(self.upload_dir, "images", category)
            image_path = os.path.join(image_dir, filename)
            
            # 读取并处理图片
            file_content = await file.read()
            image = Image.open(file.file)
            
            # 优化图片
            optimized_image = self._optimize_image(image)
            
            # 保存优化后的图片
            optimized_image.save(image_path, format='JPEG', quality=85, optimize=True)
            
            # 获取图片信息
            file_size = os.path.getsize(image_path)
            width, height = optimized_image.size
            
            # 生成访问URL
            relative_path = os.path.join("images", category, filename).replace("\\", "/")
            image_url = f"{settings.api_base_url}/uploads/{relative_path}"
            
            result = {
                "filename": filename,
                "original_filename": file.filename,
                "category": category,
                "file_path": image_path,
                "file_size": file_size,
                "width": width,
                "height": height,
                "url": image_url,
                "content_type": "image/jpeg",
                "uploaded_by": user_id,
                "uploaded_at": datetime.now().isoformat(),
                "thumbnails": {}
            }
            
            # 创建缩略图
            if create_thumbnails:
                thumbnail_dir = os.path.join(self.upload_dir, "thumbnails", category)
                
                for size_name, size in self.THUMBNAIL_SIZES.items():
                    try:
                        thumbnail = self._create_thumbnail(optimized_image, size)
                        thumbnail_filename = f"thumb_{size_name}_{filename}"
                        thumbnail_path = os.path.join(thumbnail_dir, thumbnail_filename)
                        
                        thumbnail.save(thumbnail_path, format='JPEG', quality=80, optimize=True)
                        
                        thumbnail_relative_path = os.path.join("thumbnails", category, thumbnail_filename).replace("\\", "/")
                        thumbnail_url = f"{settings.api_base_url}/uploads/{thumbnail_relative_path}"
                        
                        result["thumbnails"][size_name] = {
                            "filename": thumbnail_filename,
                            "url": thumbnail_url,
                            "width": size[0],
                            "height": size[1],
                            "file_size": os.path.getsize(thumbnail_path)
                        }
                        
                    except Exception as e:
                        logger.error(f"创建缩略图失败 {size_name}: {str(e)}")
            
            logger.info(f"图片保存成功: {filename}, 用户: {user_id}, 分类: {category}")
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"保存图片失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"保存图片失败: {str(e)}"
            )
    
    def delete_image(self, filename: str, category: str) -> bool:
        """删除图片及其缩略图"""
        try:
            # 删除主图片
            image_path = os.path.join(self.upload_dir, "images", category, filename)
            if os.path.exists(image_path):
                os.remove(image_path)
            
            # 删除缩略图
            thumbnail_dir = os.path.join(self.upload_dir, "thumbnails", category)
            for size_name in self.THUMBNAIL_SIZES.keys():
                thumbnail_filename = f"thumb_{size_name}_{filename}"
                thumbnail_path = os.path.join(thumbnail_dir, thumbnail_filename)
                if os.path.exists(thumbnail_path):
                    os.remove(thumbnail_path)
            
            logger.info(f"图片删除成功: {filename}, 分类: {category}")
            return True
            
        except Exception as e:
            logger.error(f"删除图片失败: {filename}, 错误: {str(e)}")
            return False
    
    def get_image_info(self, filename: str, category: str) -> Optional[Dict[str, Any]]:
        """获取图片信息"""
        try:
            image_path = os.path.join(self.upload_dir, "images", category, filename)
            
            if not os.path.exists(image_path):
                return None
            
            # 获取文件信息
            file_size = os.path.getsize(image_path)
            file_stat = os.stat(image_path)
            
            # 获取图片尺寸
            with Image.open(image_path) as image:
                width, height = image.size
            
            relative_path = os.path.join("images", category, filename).replace("\\", "/")
            image_url = f"{settings.api_base_url}/uploads/{relative_path}"
            
            return {
                "filename": filename,
                "category": category,
                "file_size": file_size,
                "width": width,
                "height": height,
                "url": image_url,
                "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                "modified_at": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取图片信息失败: {filename}, 错误: {str(e)}")
            return None


# 创建全局图片处理器实例
image_handler = ImageHandler()
