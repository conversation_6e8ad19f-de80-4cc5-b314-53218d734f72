# Nginx配置示例 - 燕友圈榜单系统
# 将此配置放在 /etc/nginx/sites-available/ 目录下

server {
    listen 80;
    server_name yysls.sappan.top;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yysls.sappan.top;
    
    # SSL配置（请替换为你的证书路径）
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 客户端最大请求体大小（用于文件上传）
    client_max_body_size 10M;
    
    # 静态文件处理 - 直接由Nginx提供服务
    location /uploads/ {
        alias /path/to/your/project/yysls-ranking-backend/uploads/;
        
        # 缓存设置
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 安全头
        add_header X-Content-Type-Options nosniff;
        
        # 允许跨域访问（如果需要）
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
        
        # 文件不存在时返回404
        try_files $uri =404;
    }
    
    # 静态资源处理
    location /static/ {
        alias /path/to/your/project/yysls-ranking-backend/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API请求代理到FastAPI应用
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查和根路径
    location ~ ^/(health|$) {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 其他请求返回404
    location / {
        return 404;
    }
    
    # 日志配置
    access_log /var/log/nginx/yysls_access.log;
    error_log /var/log/nginx/yysls_error.log;
}

# 如果你使用的是子域名或不同的域名配置，请相应调整server_name
