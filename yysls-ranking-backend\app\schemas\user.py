"""
用户相关的Pydantic模型
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, field_validator

from app.models.user import UserRole, UserGender


class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    nickname: Optional[str] = Field(None, max_length=100, description="昵称")
    avatar_url: Optional[str] = Field(None, max_length=500, description="头像URL")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    bio: Optional[str] = Field(None, description="个人简介")
    level: Optional[str] = Field(default="江湖新人", max_length=50, description="用户等级")

    # 新增字段
    location: Optional[str] = Field(None, max_length=200, description="所在地")
    user_number: Optional[str] = Field(None, min_length=3, max_length=50, description="用户编号")
    gender: Optional[UserGender] = Field(None, description="性别")
    age: Optional[int] = Field(None, ge=0, le=150, description="年龄")

    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v):
        if v and not v.isdigit():
            raise ValueError('手机号只能包含数字')
        return v




class UserCreate(UserBase):
    """创建用户模型"""
    password: str = Field(..., min_length=6, max_length=128, description="密码")
    role: UserRole = Field(default=UserRole.USER, description="用户角色")

    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('密码长度至少6位')
        return v


class UserUpdate(BaseModel):
    """更新用户模型"""
    id:Optional[int] = Field(None, description="用户ID")
    nickname: Optional[str] = Field(None, max_length=100, description="昵称")
    avatar_url: Optional[str] = Field(None, max_length=500, description="头像URL")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    bio: Optional[str] = Field(None, description="个人简介")
    level: Optional[str] = Field(None, max_length=50, description="用户等级")
    is_active: Optional[bool] = Field(None, description="是否激活")
    role: Optional[UserRole] = Field(None, description="用户角色")

    # 新增字段
    location: Optional[str] = Field(None, max_length=200, description="所在地")
    user_number: Optional[str] = Field(None, min_length=3, max_length=50, description="用户编号")
    gender: Optional[UserGender] = Field(None, description="性别")
    age: Optional[int] = Field(None, ge=0, le=150, description="年龄")

    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v):
        if v and not v.isdigit():
            raise ValueError('手机号只能包含数字')
        return v


class UserResponse(UserBase):
    """用户响应模型"""
    id: int = Field(description="用户ID")
    role: UserRole = Field(description="用户角色")
    is_active: bool = Field(description="是否激活")
    is_verified: bool = Field(description="是否验证")
    points: int = Field(description="积分")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """用户登录模型"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class UserRegister(UserCreate):
    """用户注册模型"""
    confirm_password: str = Field(..., description="确认密码")

    @field_validator('confirm_password')
    @classmethod
    def passwords_match(cls, v, info):
        if info.data.get('password') and v != info.data['password']:
            raise ValueError('两次输入的密码不一致')
        return v


class WeChatLoginRequest(BaseModel):
    """微信登录请求模型"""
    code: str = Field(..., description="微信授权码")
    user_info: Optional[dict] = Field(None, description="用户信息")


class PasswordChangeRequest(BaseModel):
    """修改密码请求模型"""
    old_password: str = Field(..., description="原密码")
    new_password: str = Field(..., min_length=6, max_length=128, description="新密码")
    confirm_password: str = Field(..., description="确认新密码")
    
    @field_validator('confirm_password')
    @classmethod
    def passwords_match(cls, v, info):
        if info.data.get('new_password') and v != info.data['new_password']:
            raise ValueError('两次输入的密码不一致')
        return v


class UserSearchRequest(BaseModel):
    """用户搜索请求模型"""
    keyword: str = Field(..., min_length=1, description="搜索关键词")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")


class UserListResponse(BaseModel):
    """用户列表响应模型"""
    users: list[UserResponse] = Field(description="用户列表")
    total: int = Field(description="总数量")
    page: int = Field(description="当前页码")
    size: int = Field(description="每页大小")
    pages: int = Field(description="总页数")
