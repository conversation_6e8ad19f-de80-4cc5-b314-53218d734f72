#!/usr/bin/env python3
"""
测试深层次目录访问
"""
import os
import requests
from app.config import settings

def create_test_files():
    """创建测试文件"""
    print("创建测试文件...")
    
    # 确保目录存在
    test_dirs = [
        "uploads/test/level1",
        "uploads/test/level1/level2", 
        "uploads/test/level1/level2/level3"
    ]
    
    for dir_path in test_dirs:
        os.makedirs(dir_path, exist_ok=True)
        
        # 在每个目录创建一个测试文件
        test_file = os.path.join(dir_path, "test.txt")
        with open(test_file, "w", encoding="utf-8") as f:
            f.write(f"这是测试文件，位于: {dir_path}")
        
        print(f"✓ 创建文件: {test_file}")

def test_directory_access():
    """测试不同深度的目录访问"""
    print("\n=== 测试深层次目录访问 ===")
    
    base_url = "http://localhost:8000"  # 本地测试
    
    # 测试不同深度的文件访问
    test_urls = [
        f"{base_url}/uploads/test/level1/test.txt",
        f"{base_url}/uploads/test/level1/level2/test.txt", 
        f"{base_url}/uploads/test/level1/level2/level3/test.txt"
    ]
    
    for url in test_urls:
        print(f"\n测试URL: {url}")
        try:
            response = requests.get(url, timeout=5)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✓ 访问成功")
                print(f"内容: {response.text}")
            else:
                print(f"✗ 访问失败: {response.text}")
                
        except Exception as e:
            print(f"✗ 请求异常: {str(e)}")

def test_existing_structure():
    """测试现有的目录结构"""
    print("\n=== 测试现有目录结构 ===")
    
    base_url = "http://localhost:8000"
    
    # 检查现有目录结构
    upload_dir = settings.upload_dir_absolute
    print(f"上传目录: {upload_dir}")
    
    # 遍历uploads目录，找到实际文件
    for root, dirs, files in os.walk(upload_dir):
        for file in files:
            if file.endswith(('.jpg', '.jpeg', '.png', '.txt')):
                file_path = os.path.join(root, file)
                # 计算相对于uploads目录的路径
                rel_path = os.path.relpath(file_path, upload_dir)
                # 转换为URL路径
                url_path = rel_path.replace("\\", "/")
                test_url = f"{base_url}/uploads/{url_path}"
                
                print(f"\n文件: {file_path}")
                print(f"URL: {test_url}")
                
                # 测试访问
                try:
                    response = requests.head(test_url, timeout=5)
                    if response.status_code == 200:
                        print("✓ 可访问")
                    else:
                        print(f"✗ 不可访问 ({response.status_code})")
                except Exception as e:
                    print(f"✗ 请求失败: {str(e)}")
                
                # 只测试前5个文件
                break
        if len(files) > 0:
            break

def show_directory_structure():
    """显示目录结构"""
    print("\n=== 当前目录结构 ===")
    
    upload_dir = settings.upload_dir_absolute
    print(f"上传目录: {upload_dir}")
    
    def print_tree(directory, prefix="", max_depth=3, current_depth=0):
        if current_depth >= max_depth:
            return
            
        try:
            items = sorted(os.listdir(directory))
            for i, item in enumerate(items):
                if item.startswith('.'):
                    continue
                    
                item_path = os.path.join(directory, item)
                is_last = i == len(items) - 1
                
                current_prefix = "└── " if is_last else "├── "
                print(f"{prefix}{current_prefix}{item}")
                
                if os.path.isdir(item_path):
                    next_prefix = prefix + ("    " if is_last else "│   ")
                    print_tree(item_path, next_prefix, max_depth, current_depth + 1)
                    
        except PermissionError:
            print(f"{prefix}[权限不足]")
    
    print_tree(upload_dir)

def main():
    """主函数"""
    print("=== FastAPI StaticFiles 深层次目录访问测试 ===")
    
    # 显示目录结构
    show_directory_structure()
    
    # 创建测试文件
    create_test_files()
    
    # 测试深层次访问
    test_directory_access()
    
    # 测试现有文件
    test_existing_structure()
    
    print("\n=== 总结 ===")
    print("FastAPI的StaticFiles挂载支持深层次目录访问：")
    print("1. 挂载 /uploads 到 uploads/ 目录")
    print("2. 可以访问 /uploads/images/temp/file.jpg")
    print("3. 可以访问 /uploads/thumbnails/sponsors/thumb.png")
    print("4. 可以访问任意深度的子目录文件")
    print("\n如果访问失败，可能的原因：")
    print("- 文件不存在")
    print("- 权限不足")
    print("- 路径配置错误")
    print("- 服务器配置问题（如Nginx代理）")

if __name__ == "__main__":
    main()
