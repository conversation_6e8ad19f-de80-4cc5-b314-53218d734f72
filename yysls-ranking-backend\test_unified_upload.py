#!/usr/bin/env python3
"""
测试统一文件上传接口
"""
import requests
import os
from io import BytesIO
from PIL import Image

# 配置
BASE_URL = "http://localhost:8000"
LOGIN_URL = f"{BASE_URL}/api/v1/auth/login"
UPLOAD_URL = f"{BASE_URL}/api/v1/upload/file"

def login_and_get_token():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(LOGIN_URL, json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data["data"]["access_token"]
    else:
        print(f"登录失败: {response.text}")
        return None

def create_test_image():
    """创建测试图片"""
    image = Image.new("RGB", (200, 200), "red")
    image_bytes = BytesIO()
    image.save(image_bytes, format="JPEG")
    image_bytes.seek(0)
    return image_bytes

def test_image_upload(token):
    """测试图片上传"""
    print("测试图片上传...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 创建测试图片
    image_data = create_test_image()
    
    files = {
        "file": ("test_image.jpg", image_data, "image/jpeg")
    }
    
    response = requests.post(UPLOAD_URL, headers=headers, files=files)
    
    if response.status_code == 200:
        data = response.json()
        print("✓ 图片上传成功")
        print(f"  文件类型: {data['data']['file_type']}")
        print(f"  原始文件名: {data['data']['original_filename']}")
        print(f"  完整访问URL: {data['data']['temp_filepath']}")
        print(f"  文件大小: {data['data']['file_size']} bytes")
        print(f"  图片尺寸: {data['data']['width']}x{data['data']['height']}")
        print(f"  缩略图数量: {len(data['data']['thumbnails'])}")
        print("  缩略图URL:")
        for size, thumb_info in data['data']['thumbnails'].items():
            print(f"    {size}: {thumb_info['url']}")
        return data['data']
    else:
        print(f"✗ 图片上传失败: {response.text}")
        return None

def test_excel_upload(token):
    """测试Excel上传"""
    print("\n测试Excel上传...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 检查是否有测试Excel文件
    excel_file_path = "榜单明细模板.xlsx"
    if not os.path.exists(excel_file_path):
        print("✗ 测试Excel文件不存在，跳过Excel上传测试")
        return None
    
    with open(excel_file_path, "rb") as f:
        files = {
            "file": ("test_excel.xlsx", f, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        }
        
        response = requests.post(UPLOAD_URL, headers=headers, files=files)
    
    if response.status_code == 200:
        data = response.json()
        print("✓ Excel上传成功")
        print(f"  文件类型: {data['data']['file_type']}")
        print(f"  原始文件名: {data['data']['original_filename']}")
        print(f"  临时文件名: {data['data']['temp_filename']}")
        print(f"  文件大小: {data['data']['file_size']} bytes")
        print(f"  过期时间: {data['data']['expires_at']}")
        return data['data']
    else:
        print(f"✗ Excel上传失败: {response.text}")
        return None

def test_unsupported_file(token):
    """测试不支持的文件类型"""
    print("\n测试不支持的文件类型...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 创建一个文本文件
    text_data = BytesIO(b"This is a test text file")
    
    files = {
        "file": ("test.txt", text_data, "text/plain")
    }
    
    response = requests.post(UPLOAD_URL, headers=headers, files=files)
    
    if response.status_code == 400:
        print("✓ 正确拒绝了不支持的文件类型")
        print(f"  错误信息: {response.json()['detail']}")
    else:
        print(f"✗ 应该拒绝不支持的文件类型，但返回了: {response.status_code}")

def test_image_access(image_data):
    """测试图片访问"""
    if not image_data or 'temp_filepath' not in image_data:
        print("\n跳过图片访问测试（没有上传成功的图片）")
        return

    print("\n测试图片访问...")

    image_url = image_data['temp_filepath']  # 现在temp_filepath就是完整URL
    response = requests.get(image_url)
    
    if response.status_code == 200:
        print("✓ 图片访问成功")
        print(f"  Content-Type: {response.headers.get('Content-Type')}")
        print(f"  Content-Length: {response.headers.get('Content-Length')}")
    else:
        print(f"✗ 图片访问失败: {response.status_code}")
    
    # 测试缩略图访问
    if 'thumbnails' in image_data and image_data['thumbnails']:
        print("\n测试缩略图访问...")
        for size, thumb_info in image_data['thumbnails'].items():
            thumb_url = f"{BASE_URL}{thumb_info['url']}"
            response = requests.get(thumb_url)
            if response.status_code == 200:
                print(f"✓ {size}缩略图访问成功")
            else:
                print(f"✗ {size}缩略图访问失败: {response.status_code}")

def main():
    """主测试函数"""
    print("=== 统一文件上传接口测试 ===")
    
    # 登录获取token
    token = login_and_get_token()
    if not token:
        print("无法获取认证token，测试终止")
        return
    
    print(f"✓ 登录成功，获取到token")
    
    # 测试图片上传
    image_data = test_image_upload(token)
    
    # 测试Excel上传
    excel_data = test_excel_upload(token)
    
    # 测试不支持的文件类型
    test_unsupported_file(token)
    
    # 测试图片访问
    test_image_access(image_data)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
