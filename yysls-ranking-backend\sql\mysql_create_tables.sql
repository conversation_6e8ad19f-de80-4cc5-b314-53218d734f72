-- 燕友圈榜单系统数据库建表脚本 (MySQL 8.0)
-- 生成时间: 2024年当前日期
-- 数据库类型: MySQL 8.0+
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci

-- 设置字符集和时区
SET NAMES utf8mb4;
SET time_zone = '+08:00';

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS yysls_ranking 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE yysls_ranking;

-- 1. 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NULL COMMENT '用户名',
    password_hash VARCHAR(255) NULL COMMENT '密码哈希',
    nickname VARCHAR(100) NOT NULL DEFAULT '' COMMENT '昵称',
    avatar_url VARCHAR(500) NULL COMMENT '头像URL',
    email VARCHAR(100) UNIQUE NULL COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE NULL COMMENT '手机号',
    
    -- 微信信息
    wechat_openid VARCHAR(100) UNIQUE NULL COMMENT '微信OpenID',
    wechat_unionid VARCHAR(100) UNIQUE NULL COMMENT '微信UnionID',
    
    -- 角色和状态
    role VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '用户角色',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    is_verified BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否验证',
    
    -- 扩展信息
    bio TEXT NULL COMMENT '个人简介',
    level VARCHAR(50) DEFAULT '江湖新人' COMMENT '用户等级',
    points INT NOT NULL DEFAULT 0 COMMENT '积分',

    -- 新增用户信息字段
    location VARCHAR(200) NULL COMMENT '所在地',
    user_number VARCHAR(50) UNIQUE NULL COMMENT '用户编号',
    gender VARCHAR(20) NULL COMMENT '性别',
    age INT NULL COMMENT '年龄',
    
    -- 时间戳
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    
    -- 索引
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_wechat_openid (wechat_openid),
    INDEX idx_user_number (user_number),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at),

    -- 约束
    CHECK (role IN ('user', 'admin')),
    CHECK (age IS NULL OR (age >= 0 AND age <= 150)),
    CHECK (gender IS NULL OR gender IN ('男', '女', '不方便透露'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 榜单表
CREATE TABLE rankings (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '榜单ID',

    -- 基础信息
    name VARCHAR(200) NOT NULL COMMENT '榜单名称',
    period INT NOT NULL COMMENT '期数',
    ranking_type VARCHAR(20) NOT NULL COMMENT '榜单类型',

    -- 时间信息
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',

    -- 配置信息
    team_size_limit INT NOT NULL COMMENT '组队人数限制',
    total_participants INT NOT NULL DEFAULT 0 COMMENT '总参与人数',

    -- 状态信息
    status VARCHAR(20) NOT NULL DEFAULT 'not_started' COMMENT '榜单状态',

    -- 管理信息
    created_by INT NOT NULL COMMENT '创建人ID',
    updated_by INT NULL COMMENT '更新人ID',

    -- 时间戳
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_ranking_type (ranking_type),
    INDEX idx_status (status),
    INDEX idx_period (period),
    INDEX idx_created_at (created_at),
    INDEX idx_start_time (start_time),
    INDEX idx_created_by (created_by),

    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),

    -- 约束
    CHECK (ranking_type IN ('5_person', '10_person')),
    CHECK (status IN ('not_started', 'in_progress', 'finished'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='榜单表';

-- 3. 榜单明细表
CREATE TABLE ranking_details (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '明细ID',

    -- 关联信息
    ranking_id INT NOT NULL COMMENT '榜单ID',

    -- 排名信息
    rank_start INT NOT NULL COMMENT '排名开始',
    rank_end INT NOT NULL COMMENT '排名结束',

    -- 时间信息
    completion_time TIME NOT NULL COMMENT '完成时间(分秒)',
    completion_seconds INT NOT NULL COMMENT '完成时间(总秒数)',

    -- 参与信息
    participant_count INT NOT NULL COMMENT '当前时间区间参与人数',
    team_info TEXT NULL COMMENT '队伍信息(JSON格式)',
    team_name VARCHAR(100) NULL COMMENT '队伍名称',

    -- 时间戳
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_ranking_id (ranking_id),
    INDEX idx_completion_seconds (completion_seconds),
    INDEX idx_created_at (created_at),

    -- 外键约束
    FOREIGN KEY (ranking_id) REFERENCES rankings(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='榜单明细表';

-- 4. 赞助商表
CREATE TABLE sponsors (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '赞助商ID',
    name VARCHAR(200) NOT NULL COMMENT '赞助商名称',
    logo_url VARCHAR(500) NULL COMMENT 'Logo URL（用作头像）',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',

    -- 时间戳
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='赞助商表';

-- 5. 系统配置表
CREATE TABLE system_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NULL COMMENT '配置值',
    config_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(500) NULL COMMENT '配置描述',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开（前端可访问）',
    
    -- 时间戳
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_config_key (config_key),
    INDEX idx_config_type (config_type),
    INDEX idx_is_public (is_public),
    
    -- 约束
    CHECK (config_type IN ('string', 'integer', 'boolean', 'json'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 6. 内容表
CREATE TABLE contents (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '内容ID',
    content_type VARCHAR(50) NOT NULL COMMENT '内容类型',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    content TEXT NULL COMMENT '内容',
    is_published BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已发布',
    publish_at TIMESTAMP NULL COMMENT '发布时间',
    
    -- 时间戳
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_content_type (content_type),
    INDEX idx_is_published (is_published),
    INDEX idx_publish_at (publish_at),
    INDEX idx_created_at (created_at),
    
    -- 约束
    CHECK (content_type IN ('announcement', 'about', 'privacy', 'terms'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容表';

-- 7. 播报消息表
CREATE TABLE broadcast_messages (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '消息ID',
    message TEXT NOT NULL COMMENT '消息内容',
    message_type VARCHAR(20) NOT NULL DEFAULT 'info' COMMENT '消息类型',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    
    -- 时间戳
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_is_active (is_active),
    INDEX idx_message_type (message_type),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_created_at (created_at),
    
    -- 约束
    CHECK (message_type IN ('info', 'warning', 'success', 'error'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='播报消息表';

-- 完成建表
SELECT 'MySQL tables created successfully!' AS status;
