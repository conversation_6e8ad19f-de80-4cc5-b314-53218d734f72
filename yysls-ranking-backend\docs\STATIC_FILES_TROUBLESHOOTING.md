# 静态文件访问问题排查指南

## 问题描述

文件上传成功，但访问URL时返回 `{"detail":"Not Found"}` 错误。

## 可能的原因和解决方案

### 1. FastAPI应用未重启

**问题**: 修改了静态文件配置后，应用没有重启。

**解决方案**:
```bash
# 重启FastAPI应用
# 如果使用systemd
sudo systemctl restart your-app-service

# 如果使用PM2
pm2 restart your-app

# 如果使用Docker
docker restart your-container

# 如果直接运行
# 停止当前进程，重新启动
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### 2. Nginx配置问题（推荐解决方案）

**问题**: 在生产环境中，通常使用Nginx作为反向代理，需要配置Nginx直接处理静态文件。

**解决方案**:

1. **更新Nginx配置**:
```nginx
server {
    listen 443 ssl http2;
    server_name yysls.sappan.top;
    
    # 静态文件处理 - 直接由Nginx提供服务
    location /uploads/ {
        alias /path/to/your/project/yysls-ranking-backend/uploads/;
        
        # 缓存设置
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 安全头
        add_header X-Content-Type-Options nosniff;
        
        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*";
        
        # 文件不存在时返回404
        try_files $uri =404;
    }
    
    # API请求代理到FastAPI
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

2. **重新加载Nginx配置**:
```bash
# 测试配置文件语法
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx
```

### 3. 文件路径问题

**检查步骤**:

1. **确认文件确实存在**:
```bash
# 检查文件是否存在
ls -la /path/to/your/project/yysls-ranking-backend/uploads/images/temp/

# 检查具体文件
ls -la /path/to/your/project/yysls-ranking-backend/uploads/images/temp/temp_20250805_000519_138ca98b_1.jpg
```

2. **检查文件权限**:
```bash
# 确保Nginx/Web服务器有读取权限
chmod 644 /path/to/your/project/yysls-ranking-backend/uploads/images/temp/*.jpg
chmod 755 /path/to/your/project/yysls-ranking-backend/uploads/images/temp/
```

3. **检查目录权限**:
```bash
# 确保整个路径都有执行权限
chmod 755 /path/to/your/project/yysls-ranking-backend/
chmod 755 /path/to/your/project/yysls-ranking-backend/uploads/
chmod 755 /path/to/your/project/yysls-ranking-backend/uploads/images/
```

### 4. FastAPI路由冲突

**问题**: API路由可能覆盖了静态文件路由。

**解决方案**: 已在 `main.py` 中调整了路由顺序，将静态文件挂载放在最后。

### 5. 域名和SSL问题

**检查步骤**:

1. **测试HTTP访问**:
```bash
# 如果HTTPS有问题，先测试HTTP
curl -I http://yysls.sappan.top/uploads/images/temp/temp_20250805_000519_138ca98b_1.jpg
```

2. **检查SSL证书**:
```bash
# 检查SSL证书状态
openssl s_client -connect yysls.sappan.top:443 -servername yysls.sappan.top
```

## 推荐的解决步骤

### 步骤1: 使用Nginx直接处理静态文件（推荐）

这是生产环境的最佳实践：

1. **创建Nginx配置文件**:
```bash
sudo nano /etc/nginx/sites-available/yysls
```

2. **使用提供的nginx.conf.example配置**，注意修改路径：
```nginx
location /uploads/ {
    alias /your/actual/path/yysls-ranking-backend/uploads/;
    # ... 其他配置
}
```

3. **启用站点并重启Nginx**:
```bash
sudo ln -s /etc/nginx/sites-available/yysls /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 步骤2: 检查文件和权限

```bash
# 检查文件存在
ls -la /your/path/uploads/images/temp/temp_20250805_000519_138ca98b_1.jpg

# 设置正确权限
chmod 644 /your/path/uploads/images/temp/*.jpg
chmod 755 /your/path/uploads/images/temp/
```

### 步骤3: 测试访问

```bash
# 测试静态文件访问
curl -I https://yysls.sappan.top/uploads/images/temp/temp_20250805_000519_138ca98b_1.jpg
```

### 步骤4: 查看日志

```bash
# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 查看Nginx访问日志
sudo tail -f /var/log/nginx/access.log

# 查看应用日志
tail -f /your/path/logs/app.log
```

## 验证解决方案

运行测试脚本验证：
```bash
cd /your/project/path
python test_static_files.py
```

## 常见错误和解决方案

### 错误1: 403 Forbidden
- **原因**: 文件或目录权限不足
- **解决**: 设置正确的文件权限（644）和目录权限（755）

### 错误2: 404 Not Found
- **原因**: 文件不存在或路径配置错误
- **解决**: 检查文件路径和Nginx配置中的alias路径

### 错误3: 502 Bad Gateway
- **原因**: FastAPI应用未运行或端口配置错误
- **解决**: 检查应用状态和端口配置

## 最佳实践

1. **使用Nginx处理静态文件**: 性能更好，减少FastAPI应用负载
2. **设置适当的缓存**: 提高静态文件访问速度
3. **配置CORS**: 如果需要跨域访问
4. **监控日志**: 定期检查访问和错误日志
5. **备份配置**: 保存工作的配置文件

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. Nginx配置文件内容
2. 应用日志
3. Nginx错误日志
4. 文件权限信息 (`ls -la` 输出)
5. 系统环境信息
