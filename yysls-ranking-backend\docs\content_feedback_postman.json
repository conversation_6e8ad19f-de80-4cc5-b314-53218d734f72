{"info": {"name": "燕友圈榜单系统 - 内容管理与用户反馈API", "description": "内容管理和用户反馈功能的API接口测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8000/api/v1", "type": "string"}, {"key": "userToken", "value": "", "type": "string"}, {"key": "adminToken", "value": "", "type": "string"}], "item": [{"name": "内容管理接口", "item": [{"name": "获取内容列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/content/contents?page=1&size=10", "host": ["{{baseUrl}}"], "path": ["content", "contents"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "content_type", "value": "announcement", "disabled": true}, {"key": "is_published", "value": "true", "disabled": true}, {"key": "search", "value": "关键词", "disabled": true}]}}}, {"name": "创建内容", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"content_type\": \"announcement\",\n  \"title\": \"系统维护公告\",\n  \"content\": \"系统将于今晚22:00-24:00进行维护，期间可能无法正常访问，请用户提前做好准备。\",\n  \"is_published\": true,\n  \"publish_at\": \"2024-01-01T22:00:00\"\n}"}, "url": {"raw": "{{baseUrl}}/content/contents", "host": ["{{baseUrl}}"], "path": ["content", "contents"]}}}, {"name": "获取内容详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/content/contents/1", "host": ["{{baseUrl}}"], "path": ["content", "contents", "1"]}}}, {"name": "更新内容", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"content_type\": \"announcement\",\n  \"title\": \"更新后的公告标题\",\n  \"content\": \"更新后的公告内容\",\n  \"is_published\": true,\n  \"publish_at\": \"2024-01-01T22:00:00\"\n}"}, "url": {"raw": "{{baseUrl}}/content/contents", "host": ["{{baseUrl}}"], "path": ["content", "contents"]}}}, {"name": "删除内容", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/content/contents/1", "host": ["{{baseUrl}}"], "path": ["content", "contents", "1"]}}}, {"name": "获取公告列表（公开）", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/content/public/announcements?limit=10", "host": ["{{baseUrl}}"], "path": ["content", "public", "announcements"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "获取关于我们（公开）", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/content/public/about", "host": ["{{baseUrl}}"], "path": ["content", "public", "about"]}}}, {"name": "获取播报消息列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/content/broadcast-messages?limit=10&is_active=true", "host": ["{{baseUrl}}"], "path": ["content", "broadcast-messages"], "query": [{"key": "limit", "value": "10"}, {"key": "is_active", "value": "true"}]}}}, {"name": "创建播报消息", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"欢迎参加燕友圈榜单挑战赛！\",\n  \"message_type\": \"info\",\n  \"is_active\": true,\n  \"start_time\": \"2024-01-01T00:00:00\",\n  \"end_time\": \"2024-01-31T23:59:59\",\n  \"display_duration\": 5,\n  \"display_order\": 1\n}"}, "url": {"raw": "{{baseUrl}}/content/broadcast-messages", "host": ["{{baseUrl}}"], "path": ["content", "broadcast-messages"]}}}]}, {"name": "用户反馈接口", "item": [{"name": "提交反馈（匿名）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"系统运行很流畅，但希望能增加夜间模式功能。\",\n  \"user_id\": null\n}"}, "url": {"raw": "{{baseUrl}}/feedback", "host": ["{{baseUrl}}"], "path": ["feedback"]}}}, {"name": "提交反馈（登录用户）", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{userToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"榜单页面加载速度有点慢，希望能优化一下。\",\n  \"user_id\": null\n}"}, "url": {"raw": "{{baseUrl}}/feedback", "host": ["{{baseUrl}}"], "path": ["feedback"]}}}, {"name": "获取我的反馈列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{userToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/feedback/my?page=1&page_size=20", "host": ["{{baseUrl}}"], "path": ["feedback", "my"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}]}}}, {"name": "管理员获取反馈列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/feedback/admin/list?page=1&page_size=20&status=pending", "host": ["{{baseUrl}}"], "path": ["feedback", "admin", "list"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}, {"key": "status", "value": "pending"}, {"key": "user_id", "value": "1", "disabled": true}, {"key": "order_by", "value": "created_at", "disabled": true}, {"key": "order_desc", "value": "true", "disabled": true}]}}}, {"name": "管理员获取反馈详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/feedback/admin/1", "host": ["{{baseUrl}}"], "path": ["feedback", "admin", "1"]}}}, {"name": "管理员更新反馈", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"resolved\",\n  \"admin_reply\": \"感谢您的反馈！夜间模式功能已加入开发计划，预计下个版本上线。\"\n}"}, "url": {"raw": "{{baseUrl}}/feedback/admin/1", "host": ["{{baseUrl}}"], "path": ["feedback", "admin", "1"]}}}, {"name": "管理员获取反馈统计", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/feedback/admin/stats", "host": ["{{baseUrl}}"], "path": ["feedback", "admin", "stats"]}}}]}]}