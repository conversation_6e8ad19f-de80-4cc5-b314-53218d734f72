"""
榜单相关的Pydantic模型
"""
from datetime import datetime, time
from typing import List, Optional
from pydantic import BaseModel, Field, validator, model_validator

from app.models.ranking import RankingStatus, RankingType


def get_ranking_type_display_name(ranking_type: RankingType) -> str:
    """获取榜单类型的中文显示名称"""
    type_mapping = {
        RankingType.FIVE_PERSON: "5人榜单",
        RankingType.TEN_PERSON: "10人榜单"
    }
    return type_mapping.get(ranking_type, str(ranking_type))


class RankingBase(BaseModel):
    """榜单基础模型"""
    name: str = Field(..., min_length=1, max_length=200, description="榜单名称")
    period: int = Field(..., ge=1, description="期数")
    ranking_type: RankingType = Field(..., description="榜单类型")
    start_time: datetime = Field(..., description="开始时间")
    end_time: datetime = Field(..., description="结束时间")
    team_size_limit: int = Field(..., ge=1, le=20, description="组队人数限制")
    
    @validator('end_time')
    def validate_end_time(cls, v, values):
        if 'start_time' in values and v <= values['start_time']:
            raise ValueError('结束时间必须晚于开始时间')
        return v
    
    @validator('team_size_limit')
    def validate_team_size_limit(cls, v, values):
        if 'ranking_type' in values:
            if values['ranking_type'] == RankingType.FIVE_PERSON and v != 5:
                raise ValueError('5人榜单的组队人数限制必须为5')
            elif values['ranking_type'] == RankingType.TEN_PERSON and v != 10:
                raise ValueError('10人榜单的组队人数限制必须为10')
        return v


class RankingCreate(RankingBase):
    """创建榜单模型"""
    excel_file_path: Optional[str] = Field(None, description="Excel文件临时路径（用于批量导入榜单明细）")
    auto_import_details: bool = Field(False, description="是否自动导入Excel中的榜单明细数据")


class RankingUpdate(BaseModel):
    """更新榜单模型"""
    id: int = Field(..., description="榜单ID")
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="榜单名称")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    status: Optional[RankingStatus] = Field(None, description="榜单状态")
    excel_file_path: Optional[str] = Field(None, description="Excel文件临时路径（用于批量更新榜单明细）")
    auto_import_details: bool = Field(False, description="是否自动导入Excel中的榜单明细数据")
    replace_existing_details: bool = Field(False, description="是否替换现有的榜单明细数据")

    @validator('end_time')
    def validate_end_time(cls, v, values):
        if v and 'start_time' in values and values['start_time'] and v <= values['start_time']:
            raise ValueError('结束时间必须晚于开始时间')
        return v


class RankingResponse(RankingBase):
    """榜单响应模型"""
    id: int = Field(description="榜单ID")
    total_participants: int = Field(description="总参与人数")
    status: RankingStatus = Field(description="榜单状态")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    ranking_type_name: str = Field(default="", description="榜单类型中文名称")

    class Config:
        from_attributes = True


class RankingDetailBase(BaseModel):
    """榜单明细基础模型"""
    rank_start: int = Field(..., ge=1, description="排名开始")
    rank_end: int = Field(..., ge=1, description="排名结束")
    completion_time: time = Field(..., description="完成时间(分秒)")
    completion_seconds: int = Field(..., ge=0, description="完成时间(总秒数)")
    team_info: Optional[str] = Field(None, description="队伍信息(JSON格式)")
    team_name: Optional[str] = Field(None, max_length=100, description="队伍名称")

    @validator('rank_end')
    def validate_rank_end(cls, v, values):
        if 'rank_start' in values and v < values['rank_start']:
            raise ValueError('排名结束必须大于等于排名开始')
        return v

    @property
    def participant_count(self) -> int:
        """根据排名区间自动计算参与人数"""
        return self.rank_end - self.rank_start + 1
    
    @validator('completion_seconds')
    def validate_completion_seconds(cls, v, values):
        if 'completion_time' in values:
            time_obj = values['completion_time']
            expected_seconds = time_obj.hour * 3600 + time_obj.minute * 60 + time_obj.second
            if v != expected_seconds:
                raise ValueError('完成时间(总秒数)与完成时间(分秒)不匹配')
        return v


class RankingDetailCreate(RankingDetailBase):
    """创建榜单明细模型"""
    ranking_id: int = Field(..., description="榜单ID")


class RankingDetailUpdate(BaseModel):
    """更新榜单明细模型"""
    rank_start: Optional[int] = Field(None, ge=1, description="排名开始")
    rank_end: Optional[int] = Field(None, ge=1, description="排名结束")
    completion_time: Optional[time] = Field(None, description="完成时间(分秒)")
    completion_seconds: Optional[int] = Field(None, ge=0, description="完成时间(总秒数)")
    team_info: Optional[str] = Field(None, description="队伍信息(JSON格式)")
    team_name: Optional[str] = Field(None, max_length=100, description="队伍名称")
    
    @validator('rank_end')
    def validate_rank_end(cls, v, values):
        if v and 'rank_start' in values and values['rank_start'] and v < values['rank_start']:
            raise ValueError('排名结束必须大于等于排名开始')
        return v


class RankingDetailResponse(RankingDetailBase):
    """榜单明细响应模型"""
    id: int = Field(description="明细ID")
    ranking_id: int = Field(description="榜单ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    class Config:
        from_attributes = True


class RankingWithDetailsResponse(RankingResponse):
    """包含明细的榜单响应模型"""
    details: List[RankingDetailResponse] = Field(default=[], description="榜单明细列表")


class RankingListRequest(BaseModel):
    """榜单列表请求模型"""
    ranking_type: Optional[RankingType] = Field(None, description="榜单类型")
    status: Optional[RankingStatus] = Field(None, description="榜单状态")
    period: Optional[int] = Field(None, ge=1, description="期数")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")


class RankingStatistics(BaseModel):
    """榜单统计模型"""
    total_rankings: int = Field(description="总榜单数")
    active_rankings: int = Field(description="进行中的榜单数")
    total_participants: int = Field(description="总参与人数")
    five_person_count: int = Field(description="5人榜单数量")
    ten_person_count: int = Field(description="10人榜单数量")
