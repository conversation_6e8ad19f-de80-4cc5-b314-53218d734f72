"""
文件上传API接口

统一处理Excel文件上传和图片上传功能
"""
import os
import uuid
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, status, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user_id
from app.schemas.common import ResponseModel
from app.utils.excel_handler import ExcelHandler
from app.utils.image_handler import image_handler
from app.config import settings
import logging

router = APIRouter()
logger = logging.getLogger(__name__)
excel_handler = ExcelHandler()


@router.post("/excel/validate", response_model=ResponseModel[Dict[str, Any]], summary="验证Excel文件")
async def validate_excel_file(
    file: UploadFile = File(..., description="Excel文件"),
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> ResponseModel[Dict[str, Any]]:
    """
    验证上传的Excel文件格式和内容
    
    - **file**: Excel文件 (.xlsx 或 .xls)
    
    返回验证结果和基本信息
    """
    try:
        # 验证文件
        await excel_handler.validate_excel_file(file)
        
        # 获取文件信息
        file_content = await file.read()
        file_size = len(file_content)
        
        # 重置文件指针
        await file.seek(0)
        
        return ResponseModel(
            code=200,
            message="Excel文件验证通过",
            data={
                "filename": file.filename,
                "size": file_size,
                "size_mb": round(file_size / (1024 * 1024), 2),
                "content_type": file.content_type,
                "is_valid": True
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证Excel文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证Excel文件失败: {str(e)}"
        )


@router.post("/excel/parse", response_model=ResponseModel[Dict[str, Any]], summary="解析Excel文件")
async def parse_excel_file(
    file: UploadFile = File(..., description="榜单明细Excel文件"),
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> ResponseModel[Dict[str, Any]]:
    """
    解析榜单明细Excel文件
    
    - **file**: 榜单明细Excel文件，必须包含指定的列格式
    
    返回解析后的榜单明细数据
    """
    try:
        # 解析Excel文件
        ranking_details = await excel_handler.parse_ranking_excel(file)
        
        return ResponseModel(
            code=200,
            message=f"Excel文件解析成功，共解析{len(ranking_details)}条记录",
            data={
                "filename": file.filename,
                "total_records": len(ranking_details),
                "ranking_details": ranking_details,
                "parsed_at": datetime.now().isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解析Excel文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"解析Excel文件失败: {str(e)}"
        )


@router.post("/excel/upload-temp", response_model=ResponseModel[Dict[str, Any]], summary="临时上传Excel文件")
async def upload_excel_temp(
    file: UploadFile = File(..., description="Excel文件"),
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> ResponseModel[Dict[str, Any]]:
    """
    临时上传Excel文件到服务器
    
    - **file**: Excel文件
    
    返回临时文件信息，用于后续的榜单创建或更新
    """
    try:
        # 验证文件
        await excel_handler.validate_excel_file(file)
        
        # 创建临时文件目录
        temp_dir = os.path.join(settings.upload_dir_absolute, "temp", "excel")
        try:
            os.makedirs(temp_dir, mode=0o755, exist_ok=True)
        except PermissionError as e:
            logger.error(f"创建上传目录失败: {temp_dir}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"服务器文件系统权限错误，请联系管理员"
            )
        
        # 生成唯一文件名
        file_extension = os.path.splitext(file.filename)[1]
        temp_filename = f"{uuid.uuid4().hex}_{current_user_id}{file_extension}"
        temp_filepath = os.path.join(temp_dir, temp_filename)
        
        # 保存文件
        file_content = await file.read()
        with open(temp_filepath, "wb") as temp_file:
            temp_file.write(file_content)
        
        # 记录文件信息
        file_info = {
            "original_filename": file.filename,
            "temp_filename": temp_filename,
            "temp_filepath": temp_filepath,
            "file_size": len(file_content),
            "content_type": file.content_type,
            "uploaded_by": current_user_id,
            "uploaded_at": datetime.now().isoformat(),
            "expires_at": datetime.now().replace(hour=23, minute=59, second=59).isoformat()  # 当天结束过期
        }
        
        logger.info(f"用户{current_user_id}临时上传Excel文件: {file.filename} -> {temp_filename}")
        
        return ResponseModel(
            code=200,
            message="Excel文件上传成功",
            data=file_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"临时上传Excel文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传Excel文件失败: {str(e)}"
        )


@router.post("/file", response_model=ResponseModel[Dict[str, Any]], summary="通用文件上传")
async def upload_file(
    request: Request,
    file: UploadFile = File(..., description="文件"),
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> ResponseModel[Dict[str, Any]]:
    """
    通用文件上传接口，支持Excel和图片文件

    - **file**: 文件 (支持 .xlsx, .xls, .jpg, .jpeg, .png, .webp, .gif)

    返回文件信息，包括临时路径和访问URL（如果是图片）
    """
    try:
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件名不能为空"
            )

        # 获取文件扩展名
        file_extension = os.path.splitext(file.filename)[1].lower()

        # 判断文件类型
        excel_extensions = {'.xlsx', '.xls'}
        image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.gif'}

        if file_extension in excel_extensions:
            # Excel文件处理逻辑
            return await _handle_excel_upload(file, current_user_id)
        elif file_extension in image_extensions:
            # 图片文件处理逻辑
            return await _handle_image_upload(file, current_user_id)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件格式，支持的格式: {', '.join(excel_extensions | image_extensions)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )


async def _handle_excel_upload(file: UploadFile, current_user_id: int) -> ResponseModel[Dict[str, Any]]:
    """处理Excel文件上传"""
    # 验证Excel文件
    await excel_handler.validate_excel_file(file)

    # 创建临时文件目录
    temp_dir = os.path.join(settings.upload_dir_absolute, "temp", "excel")
    try:
        os.makedirs(temp_dir, mode=0o755, exist_ok=True)
    except PermissionError as e:
        logger.error(f"创建上传目录失败: {temp_dir}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器文件系统权限错误，请联系管理员"
        )

    # 生成唯一文件名
    file_extension = os.path.splitext(file.filename)[1]
    temp_filename = f"{uuid.uuid4().hex}_{current_user_id}{file_extension}"
    temp_filepath = os.path.join(temp_dir, temp_filename)

    # 保存文件
    file_content = await file.read()
    with open(temp_filepath, "wb") as temp_file:
        temp_file.write(file_content)

    # 记录文件信息
    file_info = {
        "file_type": "excel",
        "original_filename": file.filename,
        "temp_filename": temp_filename,
        "temp_filepath": temp_filepath,
        "file_size": len(file_content),
        "content_type": file.content_type,
        "uploaded_by": current_user_id,
        "uploaded_at": datetime.now().isoformat(),
        "expires_at": datetime.now().replace(hour=23, minute=59, second=59).isoformat()
    }

    logger.info(f"用户{current_user_id}上传Excel文件: {file.filename} -> {temp_filename}")

    return ResponseModel(
        code=200,
        message="Excel文件上传成功",
        data=file_info
    )


async def _handle_image_upload(file: UploadFile, current_user_id: int) -> ResponseModel[Dict[str, Any]]:
    """处理图片文件上传"""
    # 验证图片文件
    await image_handler.validate_image_file(file)

    # 保存图片到临时目录，但同时生成访问URL
    image_info = await image_handler.save_image(
        file=file,
        category="temp",  # 默认保存到temp分类
        user_id=current_user_id,
        create_thumbnails=True
    )

    # 构建兼容的返回格式，保持与Excel上传一致的字段
    file_info = {
        "file_type": "image",
        "original_filename": file.filename,
        "temp_filename": image_info["filename"],
        "temp_filepath": image_info["url"],  # 使用完整URL作为temp_filepath
        "file_size": image_info["file_size"],
        "content_type": image_info["content_type"],
        "uploaded_by": current_user_id,
        "uploaded_at": image_info["uploaded_at"],
        "expires_at": datetime.now().replace(hour=23, minute=59, second=59).isoformat(),
        # 图片特有字段
        "width": image_info["width"],
        "height": image_info["height"],
        "thumbnails": image_info["thumbnails"]
    }

    logger.info(f"用户{current_user_id}上传图片文件: {file.filename} -> {image_info['filename']}")

    return ResponseModel(
        code=200,
        message="图片文件上传成功",
        data=file_info
    )


@router.get("/excel/template", summary="下载Excel模板")
async def download_excel_template(
    ranking_type: str = "5_person",
    current_user_id: int = Depends(get_current_user_id)
):
    """
    下载榜单明细Excel模板
    
    - **ranking_type**: 榜单类型 (5_person 或 10_person)
    
    返回Excel模板文件
    """
    try:
        # 验证榜单类型
        if ranking_type not in ["5_person", "10_person"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的榜单类型，支持: 5_person, 10_person"
            )
        
        # 生成Excel模板
        excel_buffer = excel_handler.create_ranking_template(ranking_type)
        
        # 生成文件名
        filename = f"榜单明细模板_{ranking_type}_{datetime.now().strftime('%Y%m%d')}.xlsx"
        
        logger.info(f"用户{current_user_id}下载Excel模板: {filename}")
        
        # 返回文件流
        return StreamingResponse(
            iter([excel_buffer.getvalue()]),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载Excel模板失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载Excel模板失败: {str(e)}"
        )


@router.delete("/excel/temp/{temp_filename}", response_model=ResponseModel[None], summary="删除临时Excel文件")
async def delete_temp_excel(
    temp_filename: str,
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> ResponseModel[None]:
    """
    删除临时上传的Excel文件
    
    - **temp_filename**: 临时文件名
    """
    try:
        # 构建文件路径
        temp_dir = os.path.join(settings.upload_dir_absolute, "temp", "excel")
        temp_filepath = os.path.join(temp_dir, temp_filename)
        
        # 检查文件是否存在
        if not os.path.exists(temp_filepath):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="临时文件不存在"
            )
        
        # 验证文件所有权（简单的安全检查）
        if not temp_filename.endswith(f"_{current_user_id}.xlsx") and not temp_filename.endswith(f"_{current_user_id}.xls"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限删除此文件"
            )
        
        # 删除文件
        os.remove(temp_filepath)
        
        logger.info(f"用户{current_user_id}删除临时Excel文件: {temp_filename}")
        
        return ResponseModel(
            code=200,
            message="临时文件删除成功",
            data=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除临时Excel文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除临时文件失败: {str(e)}"
        )


@router.get("/excel/temp/cleanup", response_model=ResponseModel[Dict[str, Any]], summary="清理过期临时文件")
async def cleanup_temp_files(
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> ResponseModel[Dict[str, Any]]:
    """
    清理过期的临时Excel文件（管理员功能）
    """
    try:
        temp_dir = os.path.join(settings.upload_dir_absolute, "temp", "excel")
        
        if not os.path.exists(temp_dir):
            return ResponseModel(
                code=200,
                message="临时目录不存在，无需清理",
                data={"deleted_count": 0}
            )
        
        # 获取当前时间
        current_time = datetime.now()
        deleted_count = 0
        
        # 遍历临时文件
        for filename in os.listdir(temp_dir):
            filepath = os.path.join(temp_dir, filename)
            
            # 检查文件修改时间
            file_mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
            
            # 如果文件超过24小时，则删除
            if (current_time - file_mtime).total_seconds() > 24 * 3600:
                try:
                    os.remove(filepath)
                    deleted_count += 1
                    logger.info(f"清理过期临时文件: {filename}")
                except Exception as e:
                    logger.error(f"删除过期文件失败 {filename}: {str(e)}")
        
        return ResponseModel(
            code=200,
            message=f"清理完成，删除{deleted_count}个过期文件",
            data={"deleted_count": deleted_count}
        )
        
    except Exception as e:
        logger.error(f"清理临时文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理临时文件失败: {str(e)}"
        )


