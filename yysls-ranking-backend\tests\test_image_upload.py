"""
图片上传功能测试
"""
import os
import tempfile
import pytest
from io import BytesIO
from PIL import Image
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.utils.image_handler import image_handler
from tests.conftest import TestDataFactory


client = TestClient(app)


class TestImageHandler:
    """图片处理器测试"""
    
    def create_test_image(self, format="JPEG", size=(100, 100), color="red"):
        """创建测试图片"""
        image = Image.new("RGB", size, color)
        image_bytes = BytesIO()
        image.save(image_bytes, format=format)
        image_bytes.seek(0)
        return image_bytes
    
    def test_validate_image_file_success(self):
        """测试图片文件验证成功"""
        # 这个测试需要模拟UploadFile，在实际项目中可能需要更复杂的设置
        pass
    
    def test_validate_image_file_invalid_extension(self):
        """测试无效文件扩展名"""
        pass
    
    def test_generate_filename(self):
        """测试文件名生成"""
        filename = image_handler._generate_filename("sponsors", 1, "test.jpg")
        
        # 检查文件名格式
        parts = filename.split('_')
        assert len(parts) >= 4
        assert parts[0] == "sponsors"
        assert filename.endswith("_1.jpg")
    
    def test_optimize_image(self):
        """测试图片优化"""
        # 创建测试图片
        test_image = Image.new("RGB", (3000, 2000), "blue")
        
        # 优化图片
        optimized = image_handler._optimize_image(test_image, max_dimension=1024)
        
        # 检查尺寸是否被正确调整
        assert max(optimized.size) <= 1024
        assert optimized.mode == "RGB"
    
    def test_create_thumbnail(self):
        """测试缩略图创建"""
        # 创建测试图片
        test_image = Image.new("RGB", (400, 300), "green")
        
        # 创建缩略图
        thumbnail = image_handler._create_thumbnail(test_image, (150, 150))
        
        # 检查缩略图尺寸
        assert thumbnail.size == (150, 150)


class TestImageUploadAPI:
    """图片上传API测试"""
    
    @pytest.fixture
    def auth_headers(self, test_user_token):
        """认证头"""
        return {"Authorization": f"Bearer {test_user_token}"}
    
    @pytest.fixture
    def admin_headers(self, test_admin_token):
        """管理员认证头"""
        return {"Authorization": f"Bearer {test_admin_token}"}
    
    def create_test_image_file(self, filename="test.jpg", format="JPEG", size=(200, 200)):
        """创建测试图片文件"""
        image = Image.new("RGB", size, "red")
        image_bytes = BytesIO()
        image.save(image_bytes, format=format)
        image_bytes.seek(0)
        return ("file", (filename, image_bytes, "image/jpeg"))
    
    def test_upload_image_success(self, auth_headers):
        """测试图片上传成功"""
        # 创建测试图片
        test_file = self.create_test_image_file()
        
        # 上传图片
        response = client.post(
            "/api/v1/images/upload",
            headers=auth_headers,
            files=[test_file],
            data={"category": "content", "create_thumbnails": "true"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "data" in data
        assert "url" in data["data"]
        assert "thumbnails" in data["data"]
    
    def test_upload_image_invalid_category(self, auth_headers):
        """测试无效分类"""
        test_file = self.create_test_image_file()
        
        response = client.post(
            "/api/v1/images/upload",
            headers=auth_headers,
            files=[test_file],
            data={"category": "invalid", "create_thumbnails": "true"}
        )
        
        assert response.status_code == 400
    
    def test_upload_image_unauthorized(self):
        """测试未授权访问"""
        test_file = self.create_test_image_file()
        
        response = client.post(
            "/api/v1/images/upload",
            files=[test_file],
            data={"category": "content", "create_thumbnails": "true"}
        )
        
        assert response.status_code == 401
    
    def test_upload_multiple_images_success(self, auth_headers):
        """测试批量上传图片成功"""
        # 创建多个测试图片
        test_files = [
            self.create_test_image_file("test1.jpg"),
            self.create_test_image_file("test2.jpg"),
            self.create_test_image_file("test3.jpg")
        ]
        
        response = client.post(
            "/api/v1/images/upload/multiple",
            headers=auth_headers,
            files=test_files,
            data={"category": "content", "create_thumbnails": "true"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "success_count" in data["data"]
        assert data["data"]["success_count"] == 3
    
    def test_upload_multiple_images_limit_exceeded(self, auth_headers):
        """测试批量上传超过限制"""
        # 创建超过限制数量的文件
        test_files = [
            self.create_test_image_file(f"test{i}.jpg") 
            for i in range(15)  # 超过10个文件的限制
        ]
        
        response = client.post(
            "/api/v1/images/upload/multiple",
            headers=auth_headers,
            files=test_files,
            data={"category": "content", "create_thumbnails": "true"}
        )
        
        assert response.status_code == 400
    
    def test_get_image_info_success(self, auth_headers):
        """测试获取图片信息成功"""
        # 首先上传一张图片
        test_file = self.create_test_image_file()
        upload_response = client.post(
            "/api/v1/images/upload",
            headers=auth_headers,
            files=[test_file],
            data={"category": "content", "create_thumbnails": "true"}
        )
        
        assert upload_response.status_code == 200
        upload_data = upload_response.json()
        filename = upload_data["data"]["filename"]
        
        # 获取图片信息
        response = client.get(
            f"/api/v1/images/info/content/{filename}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["filename"] == filename
    
    def test_get_image_info_not_found(self, auth_headers):
        """测试获取不存在的图片信息"""
        response = client.get(
            "/api/v1/images/info/content/nonexistent.jpg",
            headers=auth_headers
        )
        
        assert response.status_code == 404
    
    def test_delete_image_success(self, auth_headers):
        """测试删除图片成功"""
        # 首先上传一张图片
        test_file = self.create_test_image_file()
        upload_response = client.post(
            "/api/v1/images/upload",
            headers=auth_headers,
            files=[test_file],
            data={"category": "content", "create_thumbnails": "true"}
        )
        
        assert upload_response.status_code == 200
        upload_data = upload_response.json()
        filename = upload_data["data"]["filename"]
        
        # 删除图片
        response = client.delete(
            f"/api/v1/images/content/{filename}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
    
    def test_delete_image_not_found(self, auth_headers):
        """测试删除不存在的图片"""
        response = client.delete(
            "/api/v1/images/content/nonexistent.jpg",
            headers=auth_headers
        )
        
        assert response.status_code == 404
    
    def test_view_image_success(self):
        """测试查看图片成功"""
        # 这个测试需要先有图片文件存在
        # 在实际测试中，可能需要先上传图片或使用测试数据
        pass
    
    def test_view_image_with_thumbnail(self):
        """测试查看缩略图"""
        pass
    
    def test_download_image_success(self, auth_headers):
        """测试下载图片成功"""
        pass


class TestSponsorLogoUpload:
    """赞助商Logo上传测试"""
    
    @pytest.fixture
    def admin_headers(self, test_admin_token):
        """管理员认证头"""
        return {"Authorization": f"Bearer {test_admin_token}"}
    
    def create_test_image_file(self, filename="logo.jpg"):
        """创建测试Logo文件"""
        image = Image.new("RGB", (200, 200), "blue")
        image_bytes = BytesIO()
        image.save(image_bytes, format="JPEG")
        image_bytes.seek(0)
        return ("file", (filename, image_bytes, "image/jpeg"))
    
    def test_upload_sponsor_logo_success(self, admin_headers, db_session):
        """测试上传赞助商Logo成功"""
        # 创建测试赞助商
        sponsor_data = TestDataFactory.sponsor_data()
        create_response = client.post(
            "/api/v1/sponsors",
            headers=admin_headers,
            json=sponsor_data
        )
        
        assert create_response.status_code == 200
        sponsor_id = create_response.json()["data"]["id"]
        
        # 上传Logo
        test_file = self.create_test_image_file()
        response = client.post(
            f"/api/v1/sponsors/{sponsor_id}/upload-logo",
            headers=admin_headers,
            files=[test_file]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["logo_url"] is not None
    
    def test_upload_sponsor_logo_unauthorized(self, auth_headers, db_session):
        """测试非管理员上传Logo"""
        # 创建测试赞助商（需要管理员权限，这里假设已存在）
        sponsor_id = 1
        
        test_file = self.create_test_image_file()
        response = client.post(
            f"/api/v1/sponsors/{sponsor_id}/upload-logo",
            headers=auth_headers,  # 普通用户权限
            files=[test_file]
        )
        
        assert response.status_code == 403
    
    def test_upload_sponsor_logo_not_found(self, admin_headers):
        """测试为不存在的赞助商上传Logo"""
        test_file = self.create_test_image_file()
        response = client.post(
            "/api/v1/sponsors/99999/upload-logo",
            headers=admin_headers,
            files=[test_file]
        )
        
        assert response.status_code == 404
    
    def test_delete_sponsor_logo_success(self, admin_headers, db_session):
        """测试删除赞助商Logo成功"""
        # 这个测试需要先有带Logo的赞助商
        pass
