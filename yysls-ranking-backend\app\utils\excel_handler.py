"""
Excel文件处理工具类
"""
import os
import tempfile
from datetime import datetime, time
from typing import List, Dict, Any, Optional, Tuple
from io import BytesIO

from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.utils import get_column_letter
from fastapi import UploadFile, HTTPException

import logging


class ExcelHandler:
    """Excel文件处理工具类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 榜单明细Excel模板列定义
        self.RANKING_COLUMNS = {
            'rank_start': {'name': '排名开始', 'width': 12, 'required': True},
            'rank_end': {'name': '排名结束', 'width': 12, 'required': True},
            'completion_time': {'name': '完成时间(MM:SS)', 'width': 18, 'required': True},
            'team_name': {'name': '队伍名称', 'width': 20, 'required': False},
            'team_info': {'name': '队伍信息', 'width': 30, 'required': False}
        }
        
        # 支持的文件格式
        self.SUPPORTED_EXTENSIONS = ['.xlsx', '.xls']
        
        # 最大文件大小 (10MB)
        self.MAX_FILE_SIZE = 10 * 1024 * 1024

    def parse_shorthand_time(self, time_input: str) -> str:
        """
        解析简写时间格式并转换为MM:SS格式

        规则：
        - 30 → 00:30（30秒）
        - 130 → 01:30（1分30秒）
        - 200 → 02:00（2分钟）
        - 400 → 04:00（4分钟）
        - 530 → 05:30（5分30秒）

        Args:
            time_input: 输入的时间字符串（可能是简写格式或MM:SS格式）

        Returns:
            str: MM:SS格式的时间字符串

        Raises:
            ValueError: 当时间格式无效时
        """
        if not time_input:
            raise ValueError("时间输入不能为空")

        time_str = str(time_input).strip()

        # 如果已经是MM:SS格式，直接验证并返回
        if ':' in time_str:
            parts = time_str.split(':')
            if len(parts) == 2:
                try:
                    minutes = int(parts[0])
                    seconds = int(parts[1])
                    if 0 <= minutes <= 99 and 0 <= seconds <= 59:
                        return f"{minutes:02d}:{seconds:02d}"
                    else:
                        raise ValueError("分钟数应在0-99范围内，秒数应在0-59范围内")
                except ValueError as e:
                    if "分钟数应在" in str(e):
                        raise e
                    raise ValueError("时间格式错误，分钟和秒数必须为数字")
            else:
                raise ValueError("时间格式错误，应为MM:SS格式")

        # 处理简写格式（纯数字）
        try:
            time_num = int(time_str)
        except ValueError:
            raise ValueError("时间格式错误，应为数字或MM:SS格式")

        if time_num < 0:
            raise ValueError("时间不能为负数")

        # 根据数字位数解析
        if time_num <= 59:
            # 1-2位数字：直接作为秒数
            minutes = 0
            seconds = time_num
        elif time_num <= 999:
            # 3位数字：第1位作为分钟，后2位作为秒数
            minutes = time_num // 100
            seconds = time_num % 100
        else:
            # 4位及以上数字：前面的位数作为分钟，后2位作为秒数
            minutes = time_num // 100
            seconds = time_num % 100

        # 验证秒数，如果超过59需要进位
        if seconds > 59:
            extra_minutes = seconds // 60
            minutes += extra_minutes
            seconds = seconds % 60

        # 验证总时间合理性（不超过99分59秒）
        if minutes > 99:
            raise ValueError("时间过长，分钟数不能超过99")

        return f"{minutes:02d}:{seconds:02d}"

    async def validate_excel_file(self, file: UploadFile) -> bool:
        """
        验证Excel文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            bool: 验证是否通过
            
        Raises:
            HTTPException: 验证失败时抛出异常
        """
        try:
            # 检查文件扩展名
            if not any(file.filename.lower().endswith(ext) for ext in self.SUPPORTED_EXTENSIONS):
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的文件格式，仅支持: {', '.join(self.SUPPORTED_EXTENSIONS)}"
                )
            
            # 检查文件大小
            file_content = await file.read()
            if len(file_content) > self.MAX_FILE_SIZE:
                raise HTTPException(
                    status_code=400,
                    detail=f"文件大小超过限制，最大支持 {self.MAX_FILE_SIZE // (1024*1024)}MB"
                )
            
            # 重置文件指针
            await file.seek(0)
            
            # 尝试读取Excel文件
            try:
                workbook = load_workbook(BytesIO(file_content))
                workbook.close()
            except Exception as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Excel文件格式错误: {str(e)}"
                )
            
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"验证Excel文件失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"文件验证失败: {str(e)}"
            )
    
    async def parse_ranking_excel(self, file: UploadFile) -> List[Dict[str, Any]]:
        """
        解析榜单明细Excel文件
        
        Args:
            file: 上传的Excel文件
            
        Returns:
            List[Dict[str, Any]]: 解析后的榜单明细数据列表
            
        Raises:
            HTTPException: 解析失败时抛出异常
        """
        try:
            # 验证文件
            await self.validate_excel_file(file)
            
            # 读取文件内容
            file_content = await file.read()
            workbook = load_workbook(BytesIO(file_content))
            
            # 获取第一个工作表
            worksheet = workbook.active
            
            # 解析表头
            headers = {}
            header_row = 1
            for col_idx, cell in enumerate(worksheet[header_row], 1):
                if cell.value:
                    # 匹配列名
                    for key, config in self.RANKING_COLUMNS.items():
                        if config['name'] in str(cell.value):
                            headers[col_idx] = key
                            break
            
            if not headers:
                raise HTTPException(
                    status_code=400,
                    detail="Excel文件格式错误：未找到有效的表头"
                )
            
            # 检查必需列
            required_columns = [key for key, config in self.RANKING_COLUMNS.items() if config['required']]
            found_columns = list(headers.values())
            missing_columns = [col for col in required_columns if col not in found_columns]
            
            if missing_columns:
                missing_names = [self.RANKING_COLUMNS[col]['name'] for col in missing_columns]
                raise HTTPException(
                    status_code=400,
                    detail=f"Excel文件缺少必需列: {', '.join(missing_names)}"
                )
            
            # 解析数据行
            ranking_details = []
            errors = []
            
            for row_idx, row in enumerate(worksheet.iter_rows(min_row=2), 2):
                # 跳过空行
                if all(cell.value is None for cell in row):
                    continue
                
                try:
                    detail_data = {}
                    
                    for col_idx, key in headers.items():
                        cell_value = row[col_idx - 1].value if col_idx <= len(row) else None
                        
                        # 数据类型转换和验证
                        if key in ['rank_start', 'rank_end']:
                            if cell_value is None:
                                if self.RANKING_COLUMNS[key]['required']:
                                    errors.append(f"第{row_idx}行 {self.RANKING_COLUMNS[key]['name']} 不能为空")
                                    continue
                                detail_data[key] = None
                            else:
                                try:
                                    detail_data[key] = int(cell_value)
                                except (ValueError, TypeError):
                                    errors.append(f"第{row_idx}行 {self.RANKING_COLUMNS[key]['name']} 必须为整数")
                                    continue
                        
                        elif key == 'completion_time':
                            if cell_value is None:
                                errors.append(f"第{row_idx}行 完成时间 不能为空")
                                continue

                            # 解析时间格式，支持简写格式和MM:SS格式
                            try:
                                if isinstance(cell_value, time):
                                    # 如果已经是time对象
                                    detail_data[key] = cell_value
                                    detail_data['completion_seconds'] = cell_value.minute * 60 + cell_value.second
                                elif isinstance(cell_value, (str, int, float)):
                                    # 使用新的简写时间格式解析函数
                                    time_str = str(cell_value).strip()
                                    try:
                                        # 转换为MM:SS格式
                                        formatted_time = self.parse_shorthand_time(time_str)
                                        time_parts = formatted_time.split(':')
                                        minutes = int(time_parts[0])
                                        seconds = int(time_parts[1])
                                        detail_data[key] = time(minute=minutes, second=seconds)
                                        detail_data['completion_seconds'] = minutes * 60 + seconds
                                    except ValueError as e:
                                        errors.append(f"第{row_idx}行 完成时间格式错误: {str(e)}")
                                        continue
                                else:
                                    errors.append(f"第{row_idx}行 完成时间格式错误，不支持的数据类型")
                                    continue
                            except Exception as e:
                                errors.append(f"第{row_idx}行 完成时间解析失败: {str(e)}")
                                continue
                        
                        elif key in ['team_name', 'team_info']:
                            detail_data[key] = str(cell_value) if cell_value is not None else None
                    
                    # 验证排名范围
                    if 'rank_start' in detail_data and 'rank_end' in detail_data:
                        if detail_data['rank_start'] > detail_data['rank_end']:
                            errors.append(f"第{row_idx}行 排名开始不能大于排名结束")
                            continue

                        # 自动计算参与人数
                        detail_data['participant_count'] = detail_data['rank_end'] - detail_data['rank_start'] + 1

                    ranking_details.append(detail_data)
                    
                except Exception as e:
                    errors.append(f"第{row_idx}行 数据解析错误: {str(e)}")
                    continue
            
            workbook.close()
            
            # 如果有错误，抛出异常
            if errors:
                raise HTTPException(
                    status_code=400,
                    detail=f"Excel数据解析错误:\n" + "\n".join(errors[:10])  # 最多显示10个错误
                )
            
            if not ranking_details:
                raise HTTPException(
                    status_code=400,
                    detail="Excel文件中没有有效的数据行"
                )
            
            self.logger.info(f"成功解析Excel文件，共{len(ranking_details)}条记录")
            return ranking_details
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"解析Excel文件失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Excel文件解析失败: {str(e)}"
            )
    
    def create_ranking_template(self, ranking_type: str = "5_person") -> BytesIO:
        """
        创建榜单明细Excel模板
        
        Args:
            ranking_type: 榜单类型
            
        Returns:
            BytesIO: Excel文件的字节流
        """
        try:
            workbook = Workbook()
            worksheet = workbook.active
            worksheet.title = "榜单明细模板"
            
            # 设置样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            border = Border(
                left=Side(style="thin"),
                right=Side(style="thin"),
                top=Side(style="thin"),
                bottom=Side(style="thin")
            )
            
            # 写入表头
            col_idx = 1
            for key, config in self.RANKING_COLUMNS.items():
                cell = worksheet.cell(row=1, column=col_idx)
                cell.value = config['name']
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = border
                
                # 设置列宽
                worksheet.column_dimensions[get_column_letter(col_idx)].width = config['width']
                col_idx += 1
            
            # 添加示例数据（移除了参与人数列，系统会自动计算）
            example_data = [
                [1, 5, "05:30", "燕友圈战队", "队伍A,队伍B,队伍C,队伍D,队伍E"],
                [6, 10, "330", "竞速联盟", "队伍F,队伍G,队伍H,队伍I,队伍J"],  # 使用简写格式示例
                [11, 15, "07:00", "速度之星", "队伍K,队伍L,队伍M,队伍N,队伍O"]
            ]
            
            for row_idx, row_data in enumerate(example_data, 2):
                for col_idx, value in enumerate(row_data, 1):
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell.value = value
                    cell.border = border
                    if col_idx <= 4:  # 数字列居中对齐
                        cell.alignment = Alignment(horizontal="center")
            
            # 添加数据验证
            # 排名开始和结束必须为正整数
            rank_validation = DataValidation(
                type="whole",
                operator="greaterThan",
                formula1=0,
                showErrorMessage=True,
                errorTitle="输入错误",
                error="排名必须为大于0的整数"
            )
            worksheet.add_data_validation(rank_validation)
            rank_validation.add(f"A2:B1000")
            

            
            # 添加说明工作表
            info_sheet = workbook.create_sheet("使用说明")
            info_sheet.append(["榜单明细Excel模板使用说明"])
            info_sheet.append([])
            info_sheet.append(["1. 请按照模板格式填写榜单明细数据"])
            info_sheet.append(["2. 排名开始和排名结束必须为正整数，且开始不能大于结束"])
            info_sheet.append(["3. 完成时间支持两种格式："])
            info_sheet.append(["   - 标准格式：MM:SS，如 05:30 表示5分30秒"])
            info_sheet.append(["   - 简写格式：纯数字，如 30→00:30, 130→01:30, 200→02:00"])
            info_sheet.append(["4. 参与人数由系统根据排名区间自动计算"])
            info_sheet.append(["5. 队伍信息为可选字段，多个队伍用逗号分隔"])
            info_sheet.append([])
            info_sheet.append(["完成时间简写格式说明："])
            info_sheet.append(["- 1-2位数字：直接作为秒数"])
            info_sheet.append(["  例如：30 → 00:30（30秒）"])
            info_sheet.append(["- 3位数字：第1位作为分钟，后2位作为秒数"])
            info_sheet.append(["  例如：130 → 01:30（1分30秒）"])
            info_sheet.append(["  例如：530 → 05:30（5分30秒）"])
            info_sheet.append(["- 4位及以上：前面位数作为分钟，后2位作为秒数"])
            info_sheet.append(["  例如：1200 → 12:00（12分钟）"])
            info_sheet.append(["- 自动进位：如果秒数超过59会自动进位到分钟"])
            info_sheet.append(["  例如：175 → 02:15（1分75秒 → 2分15秒）"])
            info_sheet.append([])
            info_sheet.append(["注意事项："])
            info_sheet.append(["- 请勿修改表头名称"])
            info_sheet.append(["- 请勿删除示例数据行，可以直接修改"])
            info_sheet.append(["- 上传前请检查数据格式是否正确"])
            info_sheet.append(["- 时间格式错误会在导入时提示具体错误信息"])
            
            # 设置说明工作表样式
            info_sheet.column_dimensions['A'].width = 50
            for row in info_sheet.iter_rows():
                for cell in row:
                    if cell.row == 1:
                        cell.font = Font(bold=True, size=14)
                    elif "注意事项" in str(cell.value):
                        cell.font = Font(bold=True, color="FF0000")
            
            # 保存到字节流
            excel_buffer = BytesIO()
            workbook.save(excel_buffer)
            excel_buffer.seek(0)
            workbook.close()
            
            self.logger.info(f"成功创建{ranking_type}榜单Excel模板")
            return excel_buffer
            
        except Exception as e:
            self.logger.error(f"创建Excel模板失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"创建Excel模板失败: {str(e)}"
            )
