#!/bin/bash

echo "=== Docker卷同步检查 ==="

# 检查命名卷是否存在
echo "1. 检查uploads_data卷是否存在："
docker volume ls | grep uploads_data

# 检查应用容器中的uploads目录
echo -e "\n2. 检查应用容器中的uploads目录："
docker exec yysls-ranking-app ls -la /app/uploads/ 2>/dev/null || echo "应用容器未运行或路径不存在"

# 检查应用容器中的目标文件
echo -e "\n3. 检查应用容器中的目标文件："
docker exec yysls-ranking-app ls -la /app/uploads/images/temp/ 2>/dev/null || echo "目录不存在或为空"

# 检查Nginx容器中的uploads目录
echo -e "\n4. 检查Nginx容器中的uploads目录："
docker exec yysls-ranking-nginx ls -la /app/uploads/ 2>/dev/null || echo "Nginx容器未运行或路径不存在"

# 检查Nginx容器中的目标文件
echo -e "\n5. 检查Nginx容器中的目标文件："
docker exec yysls-ranking-nginx ls -la /app/uploads/images/temp/ 2>/dev/null || echo "目录不存在或为空"

# 检查卷的详细信息
echo -e "\n6. 检查uploads_data卷的详细信息："
docker volume inspect uploads_data 2>/dev/null || echo "卷不存在"

# 直接检查卷内容
echo -e "\n7. 直接检查卷内容："
docker run --rm -v uploads_data:/data alpine ls -la /data/ 2>/dev/null || echo "无法访问卷内容"

echo -e "\n8. 检查目标文件是否在卷中："
docker run --rm -v uploads_data:/data alpine ls -la /data/images/temp/ 2>/dev/null || echo "目标目录不存在"

# 检查容器状态
echo -e "\n9. 检查容器运行状态："
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 检查Nginx配置中的uploads路径
echo -e "\n10. 检查Nginx配置："
docker exec yysls-ranking-nginx cat /etc/nginx/conf.d/yysls.conf | grep -A 5 -B 5 "uploads" 2>/dev/null || echo "无法读取Nginx配置"

echo -e "\n=== 建议的解决步骤 ==="
echo "如果发现问题："
echo "1. 重新创建卷: docker-compose -f docker-compose.prod.yml down -v && docker-compose -f docker-compose.prod.yml up -d"
echo "2. 检查文件权限: docker exec yysls-ranking-app chmod -R 755 /app/uploads"
echo "3. 重新上传文件测试"
echo "4. 检查Nginx配置中的域名是否正确"
