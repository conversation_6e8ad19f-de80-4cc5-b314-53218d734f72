# 统一文件上传接口使用指南

## 概述

燕友圈榜单系统提供了统一的文件上传接口 `/api/v1/upload/file`，支持Excel文件和图片文件的上传。该接口简化了前端调用逻辑，无需区分文件类型即可完成上传。

## 接口信息

- **URL**: `POST /api/v1/upload/file`
- **认证**: 需要Bearer Token
- **Content-Type**: `multipart/form-data`

## 支持的文件类型

### Excel文件
- `.xlsx` - Excel 2007+格式
- `.xls` - Excel 97-2003格式
- 最大文件大小：10MB

### 图片文件
- `.jpg`, `.jpeg` - JPEG格式
- `.png` - PNG格式
- `.webp` - WebP格式
- `.gif` - GIF格式（静态）
- 最大文件大小：5MB

## 使用方法

### JavaScript/前端调用示例

```javascript
// 通用文件上传函数
async function uploadFile(file, token) {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        const response = await fetch('/api/v1/upload/file', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: formData
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            console.log('上传成功:', result.data);
            return result.data;
        } else {
            console.error('上传失败:', result.message);
            return null;
        }
    } catch (error) {
        console.error('上传错误:', error);
        return null;
    }
}

// 使用示例
document.getElementById('fileInput').addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        const token = localStorage.getItem('access_token');
        const result = await uploadFile(file, token);
        
        if (result) {
            if (result.file_type === 'image') {
                // 图片文件处理
                console.log('图片URL:', result.temp_filepath);  // temp_filepath就是完整URL
                console.log('缩略图:', result.thumbnails);

                // 显示图片
                const img = document.createElement('img');
                img.src = result.temp_filepath;  // 直接使用temp_filepath
                document.body.appendChild(img);

            } else if (result.file_type === 'excel') {
                // Excel文件处理
                console.log('临时文件:', result.temp_filename);
                console.log('文件路径:', result.temp_filepath);
                // 可以用于后续的榜单创建或更新
            }
        }
    }
});
```

### Python调用示例

```python
import requests

def upload_file(file_path, token, base_url="http://localhost:8000"):
    """上传文件到服务器"""
    url = f"{base_url}/api/v1/upload/file"
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    with open(file_path, 'rb') as f:
        files = {
            'file': (os.path.basename(file_path), f, get_content_type(file_path))
        }
        
        response = requests.post(url, headers=headers, files=files)
    
    if response.status_code == 200:
        data = response.json()
        print(f"上传成功: {data['message']}")
        return data['data']
    else:
        print(f"上传失败: {response.text}")
        return None

def get_content_type(file_path):
    """根据文件扩展名获取Content-Type"""
    ext = os.path.splitext(file_path)[1].lower()
    content_types = {
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.xls': 'application/vnd.ms-excel',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.webp': 'image/webp',
        '.gif': 'image/gif'
    }
    return content_types.get(ext, 'application/octet-stream')

# 使用示例
token = "your_access_token_here"

# 上传图片
image_result = upload_file("logo.jpg", token)
if image_result and image_result['file_type'] == 'image':
    print(f"图片访问URL: {image_result['temp_filepath']}")  # temp_filepath就是完整URL
    print(f"图片尺寸: {image_result['width']}x{image_result['height']}")

# 上传Excel
excel_result = upload_file("data.xlsx", token)
if excel_result and excel_result['file_type'] == 'excel':
    print(f"临时文件名: {excel_result['temp_filename']}")
    print(f"文件路径: {excel_result['temp_filepath']}")
    print(f"过期时间: {excel_result['expires_at']}")
```

## 响应格式

### 成功响应

所有文件类型都包含以下通用字段：

```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "file_type": "image|excel",
    "original_filename": "原始文件名",
    "temp_filename": "服务器生成的文件名",
    "temp_filepath": "服务器文件路径",
    "file_size": 12345,
    "content_type": "MIME类型",
    "uploaded_by": 1001,
    "uploaded_at": "2024-01-15T10:30:00",
    "expires_at": "2024-01-15T23:59:59"
  }
}
```

### 图片文件额外字段

图片文件还包含以下额外字段：

```json
{
  "url": "http://localhost:8000/uploads/images/temp/filename.jpg",
  "width": 800,
  "height": 600,
  "thumbnails": {
    "small": {
      "filename": "thumb_small_filename.jpg",
      "url": "http://localhost:8000/uploads/thumbnails/temp/thumb_small_filename.jpg",
      "width": 150,
      "height": 150,
      "file_size": 8192
    },
    "medium": { /* 300x300 */ },
    "large": { /* 600x600 */ }
  }
}
```

### 错误响应

```json
{
  "code": 400,
  "message": "错误描述",
  "detail": "详细错误信息"
}
```

## 文件访问

### 图片文件访问

上传成功的图片可以通过返回的完整URL直接访问：

- **原图**: `http://localhost:8000/uploads/images/temp/filename.jpg`
- **小缩略图**: `http://localhost:8000/uploads/thumbnails/temp/thumb_small_filename.jpg`
- **中缩略图**: `http://localhost:8000/uploads/thumbnails/temp/thumb_medium_filename.jpg`
- **大缩略图**: `http://localhost:8000/uploads/thumbnails/temp/thumb_large_filename.jpg`

**注意**: URL中的host部分（`http://localhost:8000`）通过环境变量 `API_BASE_URL` 配置，在生产环境中应设置为实际的域名。

### Excel文件

Excel文件保存在临时目录中，主要用于后续的数据处理，不提供直接访问URL。

## 最佳实践

1. **文件类型检查**: 虽然服务器会验证文件类型，但建议在前端也进行初步检查
2. **文件大小限制**: 在上传前检查文件大小，避免不必要的网络传输
3. **错误处理**: 妥善处理上传失败的情况，给用户友好的提示
4. **进度显示**: 对于大文件，建议显示上传进度
5. **缩略图使用**: 对于图片展示，优先使用合适尺寸的缩略图以提高加载速度

## 常见问题

### Q: 为什么图片会自动生成缩略图？
A: 为了提高页面加载速度和用户体验，系统会自动生成多种尺寸的缩略图。

### Q: Excel文件的过期时间是多久？
A: 临时Excel文件在当天23:59:59过期，建议及时处理。

### Q: 可以上传其他格式的文件吗？
A: 目前只支持Excel和图片格式，如需支持其他格式请联系开发团队。

### Q: 上传的文件存储在哪里？
A: 文件存储在服务器的uploads目录中，通过静态文件服务提供访问。

## 与赞助商Logo上传的集成

对于赞助商Logo上传，可以先使用统一上传接口上传图片，然后使用返回的URL更新赞助商信息：

```javascript
// 1. 上传Logo图片
const logoResult = await uploadFile(logoFile, token);

// 2. 更新赞助商信息
if (logoResult && logoResult.file_type === 'image') {
    const sponsorData = {
        name: "赞助商名称",
        logo_url: logoResult.temp_filepath,  // 使用temp_filepath作为完整URL
        sort_order: 1,
        is_active: true
    };

    // 调用赞助商创建或更新接口
    await updateSponsor(sponsorId, sponsorData, token);
}
```

这种方式提供了更大的灵活性，前端可以在上传完成后预览图片，确认无误后再保存赞助商信息。
