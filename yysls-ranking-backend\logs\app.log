2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [generated in 0.01104s] {'id_1': 1}
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - [cached since 0.3025s ago] {'id_1': 1}
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - [cached since 22.01s ago] {'id_1': 1}
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - [generated in 0.00105s] {}
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - [generated in 0.00139s] {'param_1': 0, 'param_2': 2}
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 10:49:12 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 10:49:12 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-01 10:49:12 - sqlalchemy.engine.Engine - INFO - [cached since 2.741s ago] {}
2025-08-01 10:49:13 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 10:49:13 - sqlalchemy.engine.Engine - INFO - [cached since 2.765s ago] {'param_1': 0, 'param_2': 2}
2025-08-01 10:49:13 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - [generated in 0.00083s] {'id_1': 1}
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 11:56:26 - app.core.auth - ERROR - 获取当前用户ID失败: 
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [generated in 0.00179s] {'username_1': 'admin'}
2025-08-01 11:57:29 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [generated in 0.00082s] {'last_login_at': datetime.datetime(2025, 8, 1, 3, 57, 29, 922243), 'users_id': 1}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 11:57:30 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00158s] {'pk_1': 1}
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 11:57:50 - app.api.v1.endpoints.upload - INFO - 用户1临时上传Excel文件: 榜单明细模板.xlsx -> a80ca6e93c7947f0a6f361bc43c5bcdf_1.xlsx
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00143s] {'id_1': 11}
2025-08-01 12:01:38 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:01:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:01:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:01:40 - sqlalchemy.engine.Engine - INFO - [cached since 2.949s ago] {'id_1': 1}
2025-08-01 12:01:41 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:02:21 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:02:21 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:02:21 - sqlalchemy.engine.Engine - INFO - [cached since 44.02s ago] {'id_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - [cached since 44.19s ago] {'id_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - [generated in 0.00083s] {'pk_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - [generated in 0.00133s] {'ranking_id_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:02:22 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: (pymysql.err.OperationalError) (1054, "Unknown column 'ranking_details.rank_start' in 'field list'")
[SQL: SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s]
[parameters: {'ranking_id_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00161s] {'id_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [cached since 0.09514s ago] {'id_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00117s] {'pk_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.team_name, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00096s] {'ranking_id_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:11:40 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: (pymysql.err.OperationalError) (1054, "Unknown column 'ranking_details.rank_start' in 'field list'")
[SQL: SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.team_name, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s]
[parameters: {'ranking_id_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 12:29:44 - app.core.auth - ERROR - 获取当前用户ID失败: 
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 12:30:36 - sqlalchemy.engine.Engine - INFO - [generated in 0.00124s] {'username_1': 'admin'}
2025-08-01 12:30:36 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00091s] {'last_login_at': datetime.datetime(2025, 8, 1, 4, 30, 37, 39032), 'users_id': 1}
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:30:37 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00189s] {'pk_1': 1}
2025-08-01 12:30:37 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:31:08 - app.api.v1.endpoints.upload - INFO - 用户1临时上传Excel文件: 榜单明细模板.xlsx -> a6e2cf0c43474e8882f8e4d73cb1a4b3_1.xlsx
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - [generated in 0.00111s] {'id_1': 1}
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - [cached since 0.1053s ago] {'id_1': 1}
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - [generated in 0.00089s] {'pk_1': 1}
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.team_name, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - [generated in 0.00189s] {'ranking_id_1': 1}
2025-08-01 12:31:26 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:31:26 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: 'ChunkedIteratorResult' object has no attribute 'delete'
2025-08-01 12:33:23 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:33:23 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00153s] {'id_1': 1}
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - [cached since 0.08431s ago] {'id_1': 1}
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:33:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00091s] {'pk_1': 1}
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - [generated in 0.00079s] {'ranking_id_1': 1}
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:33:25 - app.utils.excel_handler - INFO - 成功解析Excel文件，共5条记录
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - [generated in 0.00087s] {'ranking_id': 1, 'rank_start': 1, 'rank_end': 5, 'completion_time': datetime.time(0, 5, 30), 'completion_seconds': 330, 'participant_count': 5, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '燕友圈战队', 'created_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802), 'updated_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802)}
2025-08-01 12:33:25 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:33:25 - RankingService - ERROR - 导入Excel榜单明细失败 ranking_id=1: (pymysql.err.OperationalError) (1364, "Field 'participant_name' doesn't have a default value")
[SQL: INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)]
[parameters: {'ranking_id': 1, 'rank_start': 1, 'rank_end': 5, 'completion_time': datetime.time(0, 5, 30), 'completion_seconds': 330, 'participant_count': 5, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '燕友圈战队', 'created_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802), 'updated_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 12:33:25 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: (pymysql.err.OperationalError) (1364, "Field 'participant_name' doesn't have a default value")
[SQL: INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)]
[parameters: {'ranking_id': 1, 'rank_start': 1, 'rank_end': 5, 'completion_time': datetime.time(0, 5, 30), 'completion_seconds': 330, 'participant_count': 5, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '燕友圈战队', 'created_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802), 'updated_at': datetime.datetime(2025, 8, 1, 4, 33, 25, 464802)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:34:22 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - [generated in 0.00147s] {'id_1': 1}
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - [cached since 0.08203s ago] {'id_1': 1}
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - [generated in 0.00111s] {'pk_1': 1}
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - [generated in 0.00103s] {'ranking_id_1': 1}
2025-08-01 12:34:23 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:34:24 - app.utils.excel_handler - INFO - 成功解析Excel文件，共5条记录
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00114s] {'ranking_id': 1, 'rank_start': 1, 'rank_end': 5, 'completion_time': datetime.time(0, 5, 30), 'completion_seconds': 330, 'participant_count': 5, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '燕友圈战队', 'created_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 150290), 'updated_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 150290)}
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - [cached since 0.0963s ago] {'ranking_id': 1, 'rank_start': 6, 'rank_end': 10, 'completion_time': datetime.time(0, 6, 15), 'completion_seconds': 375, 'participant_count': 5, 'team_info': '队伍F,队伍G,队伍H,队伍I,队伍J', 'team_name': '竞速联盟', 'created_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 245291), 'updated_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 245291)}
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - [cached since 0.197s ago] {'ranking_id': 1, 'rank_start': 11, 'rank_end': 15, 'completion_time': datetime.time(0, 7), 'completion_seconds': 420, 'participant_count': 5, 'team_info': '队伍K,队伍L,队伍M,队伍N,队伍O', 'team_name': '速度之星', 'created_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 346201), 'updated_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 346201)}
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - [cached since 0.3001s ago] {'ranking_id': 1, 'rank_start': 16, 'rank_end': 20, 'completion_time': datetime.time(0, 7, 45), 'completion_seconds': 465, 'participant_count': 5, 'team_info': '队伍P,队伍Q,队伍R,队伍S,队伍T', 'team_name': '闪电小队', 'created_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 449263), 'updated_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 449263)}
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - [cached since 0.4016s ago] {'ranking_id': 1, 'rank_start': 21, 'rank_end': 25, 'completion_time': datetime.time(0, 8, 30), 'completion_seconds': 510, 'participant_count': 5, 'team_info': '队伍U,队伍V,队伍W,队伍X,队伍Y', 'team_name': '极速团队', 'created_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 551044), 'updated_at': datetime.datetime(2025, 8, 1, 4, 34, 24, 551044)}
2025-08-01 12:34:24 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:34:26 - RankingService - INFO - 成功导入5条榜单明细数据到榜单1
2025-08-01 12:34:26 - RankingService - INFO - 清理临时Excel文件: uploads\temp\excel\a6e2cf0c43474e8882f8e4d73cb1a4b3_1.xlsx
2025-08-01 12:34:27 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:34:27 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id AS rankings_id, rankings.name AS rankings_name, rankings.period AS rankings_period, rankings.ranking_type AS rankings_ranking_type, rankings.start_time AS rankings_start_time, rankings.end_time AS rankings_end_time, rankings.team_size_limit AS rankings_team_size_limit, rankings.total_participants AS rankings_total_participants, rankings.status AS rankings_status, rankings.created_at AS rankings_created_at, rankings.updated_at AS rankings_updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:34:27 - sqlalchemy.engine.Engine - INFO - [generated in 0.00175s] {'pk_1': 1}
2025-08-01 12:34:27 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - [generated in 0.00085s] {}
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - [generated in 0.00161s] {'param_1': 0, 'param_2': 2}
2025-08-01 12:42:57 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - [cached since 40.13s ago] {}
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - [cached since 40.14s ago] {'param_1': 0, 'param_2': 2}
2025-08-01 12:43:37 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - [generated in 0.00135s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - [generated in 0.00096s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:50:36 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - [cached since 24.27s ago] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - [cached since 24.27s ago] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:51:00 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - [generated in 0.00124s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:04:38 - sqlalchemy.engine.Engine - INFO - [generated in 0.00112s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:04:39 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - [generated in 0.00151s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:05:32 - sqlalchemy.engine.Engine - INFO - [generated in 0.00279s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:05:33 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - [generated in 0.00090s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:05:41 - sqlalchemy.engine.Engine - INFO - [generated in 0.00149s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:05:42 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - [generated in 0.00110s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - [generated in 0.00139s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:05:56 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - [generated in 0.00119s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - [generated in 0.00151s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 12:07:38 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 13:56:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings 
WHERE rankings.status = %(status_1)s
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00115s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>}
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.status = %(status_1)s 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00131s] {'status_1': <RankingStatus.IN_PROGRESS: 'in_progress'>, 'param_1': 0, 'param_2': 2}
2025-08-01 13:56:30 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 15:18:17 - app.core.auth - ERROR - 获取当前用户ID失败: 
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00167s] {'username_1': 'admin'}
2025-08-01 15:18:24 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 15:18:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00097s] {'last_login_at': datetime.datetime(2025, 8, 1, 7, 18, 24, 860056), 'users_id': 1}
2025-08-01 15:18:25 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 15:18:25 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 15:18:25 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:18:25 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 15:18:25 - sqlalchemy.engine.Engine - INFO - [generated in 0.00147s] {'pk_1': 1}
2025-08-01 15:18:25 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - [cached since 89.58s ago] {'username_1': 'admin'}
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - [cached since 89.57s ago] {'last_login_at': datetime.datetime(2025, 8, 1, 7, 19, 54, 431015), 'users_id': 1}
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 15:19:54 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - [cached since 89.5s ago] {'pk_1': 1}
2025-08-01 15:19:54 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 15:20:25 - app.api.v1.endpoints.upload - INFO - 用户1临时上传Excel文件: 榜单明细模板.xlsx -> c2e5a091572d4c9fb4e4afc7ba7e3a65_1.xlsx
2025-08-01 15:20:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:20:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 15:20:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00089s] {'id_1': 2}
2025-08-01 15:20:40 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - [cached since 22.08s ago] {'id_1': 2}
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - [cached since 22.19s ago] {'id_1': 2}
2025-08-01 15:21:02 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00107s] {'pk_1': 2}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00069s] {'ranking_id_1': 2}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 15:21:03 - app.utils.excel_handler - INFO - 成功解析Excel文件，共5条记录
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00093s] {'ranking_id': 2, 'rank_start': 1, 'rank_end': 1, 'completion_time': datetime.time(1, 30), 'completion_seconds': 1800, 'participant_count': 1, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 552788), 'updated_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 552788)}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.1021s ago] {'ranking_id': 2, 'rank_start': 6, 'rank_end': 10, 'completion_time': datetime.time(0, 6, 15), 'completion_seconds': 375, 'participant_count': 5, 'team_info': '队伍F,队伍G,队伍H,队伍I,队伍J', 'team_name': '竞速联盟', 'created_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 654815), 'updated_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 654815)}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.2037s ago] {'ranking_id': 2, 'rank_start': 11, 'rank_end': 15, 'completion_time': datetime.time(0, 7), 'completion_seconds': 420, 'participant_count': 5, 'team_info': '队伍K,队伍L,队伍M,队伍N,队伍O', 'team_name': '速度之星', 'created_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 755805), 'updated_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 755805)}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.307s ago] {'ranking_id': 2, 'rank_start': 16, 'rank_end': 20, 'completion_time': datetime.time(0, 7, 45), 'completion_seconds': 465, 'participant_count': 5, 'team_info': '队伍P,队伍Q,队伍R,队伍S,队伍T', 'team_name': '闪电小队', 'created_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 858754), 'updated_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 858754)}
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-01 15:21:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.409s ago] {'ranking_id': 2, 'rank_start': 21, 'rank_end': 25, 'completion_time': datetime.time(0, 8, 30), 'completion_seconds': 510, 'participant_count': 5, 'team_info': '队伍U,队伍V,队伍W,队伍X,队伍Y', 'team_name': '极速团队', 'created_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 961164), 'updated_at': datetime.datetime(2025, 8, 1, 7, 21, 3, 961164)}
2025-08-01 15:21:04 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 15:21:04 - RankingService - INFO - 成功导入5条榜单明细数据到榜单2
2025-08-01 15:21:04 - RankingService - INFO - 清理临时Excel文件: uploads\temp\excel\c2e5a091572d4c9fb4e4afc7ba7e3a65_1.xlsx
2025-08-01 15:21:04 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 15:21:04 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id AS rankings_id, rankings.name AS rankings_name, rankings.period AS rankings_period, rankings.ranking_type AS rankings_ranking_type, rankings.start_time AS rankings_start_time, rankings.end_time AS rankings_end_time, rankings.team_size_limit AS rankings_team_size_limit, rankings.total_participants AS rankings_total_participants, rankings.status AS rankings_status, rankings.created_at AS rankings_created_at, rankings.updated_at AS rankings_updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 15:21:04 - sqlalchemy.engine.Engine - INFO - [generated in 0.00087s] {'pk_1': 2}
2025-08-01 15:21:04 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 16:13:28 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/oauth2/access_token?appid=your-wechat-app-id&secret=your-wechat-app-secret&code=0e1RCdll2eDT2g4TTJkl2cwq5Y2RCdlp&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:13:28 - app.utils.wechat - ERROR - 微信获取访问令牌失败: 40013 - invalid appid, rid: 688c7728-54e942e9-07bc5c92
2025-08-01 16:13:28 - app.utils.wechat - ERROR - 获取微信访问令牌异常: 微信API错误: invalid appid, rid: 688c7728-54e942e9-07bc5c92
2025-08-01 16:16:09 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/oauth2/access_token?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&code=0e1RCdll2eDT2g4TTJkl2cwq5Y2RCdlp&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:16:09 - app.utils.wechat - ERROR - 微信获取访问令牌失败: 40242 - invalid oauth code, it is miniprogram jscode, please use jscode2session, rid: 688c77c9-6e44118c-345043d2
2025-08-01 16:16:09 - app.utils.wechat - ERROR - 获取微信访问令牌异常: 微信API错误: invalid oauth code, it is miniprogram jscode, please use jscode2session, rid: 688c77c9-6e44118c-345043d2
2025-08-01 16:29:21 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/oauth2/access_token?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&code=0e1RCdll2eDT2g4TTJkl2cwq5Y2RCdlp&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:29:21 - app.utils.wechat - ERROR - 微信获取访问令牌失败: 40163 - code been used, rid: 688c7ae0-294fc0f1-22f5adf5
2025-08-01 16:29:21 - app.utils.wechat - ERROR - 获取微信访问令牌异常: 微信API错误: code been used, rid: 688c7ae0-294fc0f1-22f5adf5
2025-08-01 16:30:26 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/oauth2/access_token?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&code=0a1O2wll2BF83g4rmJkl2hnnOf2O2wlE&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:30:26 - app.utils.wechat - ERROR - 微信获取访问令牌失败: 40242 - invalid oauth code, it is miniprogram jscode, please use jscode2session, rid: 688c7b22-0b2a61a5-000f2662
2025-08-01 16:30:26 - app.utils.wechat - ERROR - 获取微信访问令牌异常: 微信API错误: invalid oauth code, it is miniprogram jscode, please use jscode2session, rid: 688c7b22-0b2a61a5-000f2662
2025-08-01 16:37:15 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0a1O2wll2BF83g4rmJkl2hnnOf2O2wlE&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:37:15 - app.utils.wechat - ERROR - 微信小程序登录失败: 40163 - code been used, rid: 688c7cba-7ddb3bef-5a731392
2025-08-01 16:37:15 - app.utils.wechat - ERROR - 小程序登录异常: 微信API错误: code been used, rid: 688c7cba-7ddb3bef-5a731392
2025-08-01 16:38:03 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0e1k3d2w3kONk53A7q1w3hTGw52k3d2p&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:38:03 - app.utils.wechat - INFO - 成功获取小程序session openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-01 16:38:03 - app.utils.wechat - INFO - 小程序登录成功 openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-01 16:44:13 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0e1k3d2w3kONk53A7q1w3hTGw52k3d2p&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:44:13 - app.utils.wechat - ERROR - 微信小程序登录失败: 40163 - code been used, rid: 688c7e5d-23fd6934-6adb08fb
2025-08-01 16:44:13 - app.utils.wechat - ERROR - 小程序登录异常: 微信API错误: code been used, rid: 688c7e5d-23fd6934-6adb08fb
2025-08-01 16:45:14 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0a1Bi71w3YiTl53cyb2w3AWzUg2Bi71f&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:45:14 - app.utils.wechat - INFO - 成功获取小程序session openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-01 16:45:14 - app.utils.wechat - INFO - 小程序登录成功 openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-01 16:45:15 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 16:45:15 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 16:45:15 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 16:45:15 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 16:45:15 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 16:45:15 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 16:45:16 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 16:45:16 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.wechat_openid = %(wechat_openid_1)s
2025-08-01 16:45:16 - sqlalchemy.engine.Engine - INFO - [generated in 0.00151s] {'wechat_openid_1': 'osn-d4nN8AYr6h1LNrHCwK1uD1Pw'}
2025-08-01 16:45:16 - sqlalchemy.engine.Engine - INFO - INSERT INTO users (username, password_hash, nickname, avatar_url, email, phone, wechat_openid, wechat_unionid, `role`, is_active, is_verified, bio, level, points, location, user_number, gender, age, last_login_at) VALUES (%(username)s, %(password_hash)s, %(nickname)s, %(avatar_url)s, %(email)s, %(phone)s, %(wechat_openid)s, %(wechat_unionid)s, %(role)s, %(is_active)s, %(is_verified)s, %(bio)s, %(level)s, %(points)s, %(location)s, %(user_number)s, %(gender)s, %(age)s, %(last_login_at)s)
2025-08-01 16:45:16 - sqlalchemy.engine.Engine - INFO - [generated in 0.00188s] {'username': 'wx_osn-d4nN8A', 'password_hash': None, 'nickname': '少冬瓜', 'avatar_url': '', 'email': None, 'phone': None, 'wechat_openid': 'osn-d4nN8AYr6h1LNrHCwK1uD1Pw', 'wechat_unionid': None, 'role': 'user', 'is_active': 1, 'is_verified': 1, 'bio': None, 'level': '江湖新人', 'points': 0, 'location': None, 'user_number': None, 'gender': None, 'age': None, 'last_login_at': datetime.datetime(2025, 8, 1, 8, 45, 16, 421503)}
2025-08-01 16:45:16 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 16:45:16 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 16:45:16 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 16:45:16 - sqlalchemy.engine.Engine - INFO - [generated in 0.00213s] {'pk_1': 2}
2025-08-01 16:45:16 - UserService - INFO - 微信新用户注册成功 openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-01 16:45:16 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 16:45:33 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0a1Bi71w3YiTl53cyb2w3AWzUg2Bi71f&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-01 16:45:33 - app.utils.wechat - ERROR - 微信小程序登录失败: 40163 - code been used, rid: 688c7eac-13e37e5e-42aab35d
2025-08-01 16:45:33 - app.utils.wechat - ERROR - 小程序登录异常: 微信API错误: code been used, rid: 688c7eac-13e37e5e-42aab35d
2025-08-01 17:08:23 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 17:08:23 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 17:08:23 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 17:08:23 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 17:08:23 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 17:08:23 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 17:08:23 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 17:08:23 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 17:08:23 - sqlalchemy.engine.Engine - INFO - [generated in 0.00111s] {'username_1': 'admin'}
2025-08-01 17:08:23 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 17:08:24 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 17:08:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00083s] {'last_login_at': datetime.datetime(2025, 8, 1, 9, 8, 24, 222927), 'users_id': 1}
2025-08-01 17:08:24 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 17:08:24 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 17:08:24 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 17:08:24 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 17:08:24 - sqlalchemy.engine.Engine - INFO - [generated in 0.00149s] {'pk_1': 1}
2025-08-01 17:08:24 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 17:50:57 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 17:50:57 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 17:50:57 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 17:50:57 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 17:50:58 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 17:50:58 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 17:50:58 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 17:50:58 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 17:50:58 - sqlalchemy.engine.Engine - INFO - [generated in 0.00159s] {'username_1': 'admin'}
2025-08-01 17:50:58 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 17:50:58 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 17:50:58 - sqlalchemy.engine.Engine - INFO - [generated in 0.00062s] {'last_login_at': datetime.datetime(2025, 8, 1, 9, 50, 58, 606419), 'users_id': 1}
2025-08-01 17:50:58 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 17:50:58 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 17:50:58 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 17:50:59 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 17:50:59 - sqlalchemy.engine.Engine - INFO - [generated in 0.00132s] {'pk_1': 1}
2025-08-01 17:50:59 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 17:51:03 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 17:51:03 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 17:51:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00101s] {'id_1': 1}
2025-08-01 17:51:03 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 17:52:04 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 17:52:04 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 17:52:04 - sqlalchemy.engine.Engine - INFO - [cached since 61.24s ago] {'id_1': 1}
2025-08-01 17:52:04 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 17:52:17 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 17:52:17 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 17:52:17 - sqlalchemy.engine.Engine - INFO - [cached since 74.25s ago] {'id_1': 1}
2025-08-01 17:52:17 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 17:57:45 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 17:57:45 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 17:57:46 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 17:57:46 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 17:57:46 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 17:57:46 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 17:57:46 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 17:57:46 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 17:57:46 - sqlalchemy.engine.Engine - INFO - [generated in 0.00158s] {'id_1': 1}
2025-08-01 17:57:46 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 18:00:55 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 18:00:55 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 18:00:55 - sqlalchemy.engine.Engine - INFO - [cached since 189.3s ago] {'id_1': 1}
2025-08-01 18:00:55 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 18:13:22 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 18:13:22 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:13:22 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 18:13:22 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:13:22 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 18:13:22 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:13:22 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 18:13:22 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 18:13:22 - sqlalchemy.engine.Engine - INFO - [generated in 0.00106s] {'id_1': 1}
2025-08-01 18:13:22 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 18:14:21 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 18:14:21 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:14:21 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 18:14:21 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:14:21 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 18:14:21 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:14:22 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 18:14:22 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 18:14:22 - sqlalchemy.engine.Engine - INFO - [generated in 0.00105s] {'id_1': 1}
2025-08-01 18:14:22 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 18:14:49 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 18:14:49 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:14:49 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 18:14:49 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:14:49 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 18:14:49 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:14:49 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 18:14:49 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 18:14:49 - sqlalchemy.engine.Engine - INFO - [generated in 0.00137s] {'id_1': 1}
2025-08-01 18:14:49 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 18:15:33 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 18:15:33 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:15:33 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 18:15:33 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:15:33 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 18:15:33 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:15:33 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 18:15:33 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 18:15:33 - sqlalchemy.engine.Engine - INFO - [generated in 0.00156s] {'id_1': 1}
2025-08-01 18:15:33 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 18:23:56 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 18:23:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:23:56 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 18:23:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:23:57 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 18:23:57 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 18:23:57 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 18:23:57 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 18:23:57 - sqlalchemy.engine.Engine - INFO - [generated in 0.00153s] {'username_1': 'admin'}
2025-08-01 18:23:57 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 18:23:57 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 18:23:57 - sqlalchemy.engine.Engine - INFO - [generated in 0.00085s] {'last_login_at': datetime.datetime(2025, 8, 1, 10, 23, 57, 577470), 'users_id': 1}
2025-08-01 18:23:57 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 18:23:57 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 18:23:58 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 18:23:58 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 18:23:58 - sqlalchemy.engine.Engine - INFO - [generated in 0.00152s] {'pk_1': 1}
2025-08-01 18:23:58 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 18:24:05 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 18:24:05 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 18:24:05 - sqlalchemy.engine.Engine - INFO - [generated in 0.00078s] {'id_1': 1}
2025-08-01 18:24:05 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 18:26:55 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 18:26:55 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-01 18:26:55 - sqlalchemy.engine.Engine - INFO - [cached since 170.1s ago] {'id_1': 1}
2025-08-01 18:26:55 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:16:36 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 20:16:36 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:16:36 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 20:16:36 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:16:36 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 20:16:36 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:16:36 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:16:36 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-03 20:16:36 - sqlalchemy.engine.Engine - INFO - [generated in 0.00181s] {'username_1': 'admin'}
2025-08-03 20:16:36 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-03 20:16:37 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-03 20:16:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00144s] {'last_login_at': datetime.datetime(2025, 8, 3, 12, 16, 37, 150624), 'users_id': 1}
2025-08-03 20:16:37 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:16:37 - UserService - INFO - 用户认证成功 username=admin
2025-08-03 20:16:37 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:16:37 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-03 20:16:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00156s] {'pk_1': 1}
2025-08-03 20:16:37 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:16:43 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:16:43 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:16:43 - sqlalchemy.engine.Engine - INFO - [generated in 0.00103s] {'id_1': 1}
2025-08-03 20:16:43 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:16:45 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:16:45 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:16:45 - sqlalchemy.engine.Engine - INFO - [cached since 1.633s ago] {'id_1': 1}
2025-08-03 20:16:45 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:25:21 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 20:25:21 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:25:21 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 20:25:21 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:25:21 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 20:25:21 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:25:21 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:25:21 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:25:21 - sqlalchemy.engine.Engine - INFO - [generated in 0.00143s] {'id_1': 1}
2025-08-03 20:25:21 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:27:48 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 20:27:48 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:27:48 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 20:27:48 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:27:48 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 20:27:48 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - [generated in 0.00214s] {'id_1': 1}
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.config_key = %(config_key_1)s
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - [generated in 0.00166s] {'config_key_1': 'notify'}
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - INSERT INTO system_configs (config_key, config_value, config_type, name, description, is_public, created_at, updated_at) VALUES (%(config_key)s, %(config_value)s, %(config_type)s, %(name)s, %(description)s, %(is_public)s, %(created_at)s, %(updated_at)s)
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - [generated in 0.00115s] {'config_key': 'notify', 'config_value': 'true', 'config_type': 'string', 'name': '播报管理', 'description': '播报信息管理', 'is_public': 1, 'created_at': datetime.datetime(2025, 8, 3, 12, 27, 49, 146300), 'updated_at': datetime.datetime(2025, 8, 3, 12, 27, 49, 146300)}
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.id = %(pk_1)s
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - [generated in 0.00098s] {'pk_1': 1}
2025-08-03 20:27:49 - SystemConfigService - INFO - 创建记录成功 ID=1
2025-08-03 20:27:49 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:35:15 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 20:35:15 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:35:15 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 20:35:15 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:35:15 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 20:35:15 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:35:16 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:35:16 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:35:16 - sqlalchemy.engine.Engine - INFO - [generated in 0.00153s] {'id_1': 1}
2025-08-03 20:35:16 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - [generated in 0.00153s] {'id_1': 1}
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.id = %(id_1)s
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - [generated in 0.00127s] {'id_1': 1}
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - UPDATE system_configs SET name=%(name)s, updated_at=%(updated_at)s WHERE system_configs.id = %(system_configs_id)s
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - [generated in 0.00100s] {'name': '播报管理11', 'updated_at': datetime.datetime(2025, 8, 3, 12, 35, 33, 888758), 'system_configs_id': 1}
2025-08-03 20:35:33 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:35:34 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:35:34 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.id = %(pk_1)s
2025-08-03 20:35:34 - sqlalchemy.engine.Engine - INFO - [generated in 0.00174s] {'pk_1': 1}
2025-08-03 20:35:34 - SystemConfigService - INFO - 更新记录成功 ID=1
2025-08-03 20:35:34 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - [cached since 6.814s ago] {'id_1': 1}
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.id = %(id_1)s
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - [cached since 6.812s ago] {'id_1': 1}
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - UPDATE system_configs SET config_value=%(config_value)s, updated_at=%(updated_at)s WHERE system_configs.id = %(system_configs_id)s
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00074s] {'config_value': 'false', 'updated_at': datetime.datetime(2025, 8, 3, 12, 35, 40, 695556), 'system_configs_id': 1}
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.id = %(pk_1)s
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - [cached since 6.802s ago] {'pk_1': 1}
2025-08-03 20:35:40 - SystemConfigService - INFO - 更新记录成功 ID=1
2025-08-03 20:35:40 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - [cached since 15.81s ago] {'id_1': 1}
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.id = %(id_1)s
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - [cached since 15.81s ago] {'id_1': 1}
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.id = %(pk_1)s
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - [cached since 15.76s ago] {'pk_1': 1}
2025-08-03 20:35:49 - SystemConfigService - INFO - 更新记录成功 ID=1
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - [cached since 26.51s ago] {'id_1': 1}
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.id = %(id_1)s
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - [cached since 26.51s ago] {'id_1': 1}
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - UPDATE system_configs SET config_value=%(config_value)s, updated_at=%(updated_at)s WHERE system_configs.id = %(system_configs_id)s
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - [cached since 19.7s ago] {'config_value': 'true', 'updated_at': datetime.datetime(2025, 8, 3, 12, 36, 0, 395304), 'system_configs_id': 1}
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.id = %(pk_1)s
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - [cached since 26.5s ago] {'pk_1': 1}
2025-08-03 20:36:00 - SystemConfigService - INFO - 更新记录成功 ID=1
2025-08-03 20:36:00 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:38:19 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:38:19 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:38:19 - sqlalchemy.engine.Engine - INFO - [cached since 166s ago] {'id_1': 1}
2025-08-03 20:38:19 - sqlalchemy.engine.Engine - INFO - SELECT count(system_configs.id) AS count_1 
FROM system_configs
2025-08-03 20:38:19 - sqlalchemy.engine.Engine - INFO - [generated in 0.00079s] {}
2025-08-03 20:38:19 - SystemConfigService - ERROR - 获取配置列表失败: type object 'SystemConfig' has no attribute 'sort_order'
2025-08-03 20:38:19 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:43:30 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:43:30 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:43:30 - sqlalchemy.engine.Engine - INFO - [cached since 476.9s ago] {'id_1': 1}
2025-08-03 20:43:30 - sqlalchemy.engine.Engine - INFO - SELECT count(system_configs.id) AS count_1 
FROM system_configs
2025-08-03 20:43:30 - sqlalchemy.engine.Engine - INFO - [cached since 310.9s ago] {}
2025-08-03 20:43:30 - SystemConfigService - ERROR - 获取配置列表失败: type object 'SystemConfig' has no attribute 'sort_order'
2025-08-03 20:43:30 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:59:41 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 20:59:41 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:59:41 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 20:59:41 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:59:41 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 20:59:41 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:59:41 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:59:41 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-03 20:59:41 - sqlalchemy.engine.Engine - INFO - [generated in 0.00134s] {'username_1': 'admin'}
2025-08-03 20:59:41 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-03 20:59:42 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-03 20:59:42 - sqlalchemy.engine.Engine - INFO - [generated in 0.00118s] {'last_login_at': datetime.datetime(2025, 8, 3, 12, 59, 42, 208189), 'users_id': 1}
2025-08-03 20:59:42 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:59:42 - UserService - INFO - 用户认证成功 username=admin
2025-08-03 20:59:42 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:59:42 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-03 20:59:42 - sqlalchemy.engine.Engine - INFO - [generated in 0.00251s] {'pk_1': 1}
2025-08-03 20:59:42 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:59:46 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:59:46 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:59:46 - sqlalchemy.engine.Engine - INFO - [generated in 0.00151s] {'id_1': 1}
2025-08-03 20:59:46 - sqlalchemy.engine.Engine - INFO - SELECT count(system_configs.id) AS count_1 
FROM system_configs
2025-08-03 20:59:46 - sqlalchemy.engine.Engine - INFO - [generated in 0.00141s] {}
2025-08-03 20:59:46 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs ORDER BY system_configs.config_type, system_configs.config_key 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 20:59:46 - sqlalchemy.engine.Engine - INFO - [generated in 0.00137s] {'param_1': 0, 'param_2': 2}
2025-08-03 20:59:46 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - [cached since 656.2s ago] {'id_1': 1}
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.config_key = %(config_key_1)s
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - [generated in 0.00193s] {'config_key_1': 'ad'}
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - INSERT INTO system_configs (config_key, config_value, config_type, name, description, is_public, created_at, updated_at) VALUES (%(config_key)s, %(config_value)s, %(config_type)s, %(name)s, %(description)s, %(is_public)s, %(created_at)s, %(updated_at)s)
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - [generated in 0.00140s] {'config_key': 'ad', 'config_value': 'true', 'config_type': 'string', 'name': '广告管理', 'description': '广告位相关管理', 'is_public': 1, 'created_at': datetime.datetime(2025, 8, 3, 12, 35, 43, 399464), 'updated_at': datetime.datetime(2025, 8, 3, 12, 35, 43, 399464)}
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.id = %(pk_1)s
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - [generated in 0.00096s] {'pk_1': 2}
2025-08-03 20:35:43 - SystemConfigService - INFO - 创建记录成功 ID=2
2025-08-03 20:35:43 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - [cached since 662.1s ago] {'id_1': 1}
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - SELECT count(system_configs.id) AS count_1 
FROM system_configs
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - [cached since 662.1s ago] {}
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs ORDER BY system_configs.config_type, system_configs.config_key 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - [cached since 662.1s ago] {'param_1': 0, 'param_2': 2}
2025-08-03 20:35:49 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:39:39 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:39:39 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:39:39 - sqlalchemy.engine.Engine - INFO - [cached since 891.9s ago] {'id_1': 1}
2025-08-03 20:39:39 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:39:46 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:39:46 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:39:46 - sqlalchemy.engine.Engine - INFO - [cached since 899.2s ago] {'id_1': 1}
2025-08-03 20:39:46 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:39:55 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:39:55 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:39:55 - sqlalchemy.engine.Engine - INFO - [cached since 908.8s ago] {'id_1': 1}
2025-08-03 20:39:55 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:40:03 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:40:03 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:40:03 - sqlalchemy.engine.Engine - INFO - [cached since 916.7s ago] {'id_1': 1}
2025-08-03 20:40:03 - sqlalchemy.engine.Engine - INFO - SELECT count(users.id) AS count_1 
FROM users
2025-08-03 20:40:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00091s] {}
2025-08-03 20:40:03 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 20:40:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00098s] {'param_1': 0, 'param_2': 20}
2025-08-03 20:40:03 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:44:49 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 20:44:49 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:44:49 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 20:44:49 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:44:49 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 20:44:49 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 20:44:50 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:44:50 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:44:50 - sqlalchemy.engine.Engine - INFO - [generated in 0.00188s] {'id_1': 1}
2025-08-03 20:44:50 - sqlalchemy.engine.Engine - INFO - SELECT count(users.id) AS count_1 
FROM users
2025-08-03 20:44:50 - sqlalchemy.engine.Engine - INFO - [generated in 0.00096s] {}
2025-08-03 20:44:50 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 20:44:50 - sqlalchemy.engine.Engine - INFO - [generated in 0.00170s] {'param_1': 0, 'param_2': 20}
2025-08-03 20:44:50 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - [cached since 62.38s ago] {'id_1': 1}
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - [cached since 62.42s ago] {'id_1': 2}
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - UPDATE users SET nickname=%(nickname)s, `role`=%(role)s, updated_at=CURRENT_TIMESTAMP WHERE users.id = %(users_id)s
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - [generated in 0.00139s] {'nickname': '少冬瓜1', 'role': <UserRole.ADMIN: 'admin'>, 'users_id': 2}
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - [generated in 0.00160s] {'pk_1': 2}
2025-08-03 20:45:52 - UserService - INFO - 更新记录成功 ID=2
2025-08-03 20:45:52 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 21:07:52 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 21:07:52 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:07:52 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 21:07:52 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:07:52 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 21:07:52 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:07:52 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:07:52 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 21:07:52 - sqlalchemy.engine.Engine - INFO - [generated in 0.00167s] {'id_1': 1}
2025-08-03 21:07:52 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 21:07:59 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:07:59 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 21:07:59 - sqlalchemy.engine.Engine - INFO - [cached since 6.44s ago] {'id_1': 1}
2025-08-03 21:07:59 - sqlalchemy.engine.Engine - INFO - SELECT count(contents.id) AS count_1 
FROM contents
2025-08-03 21:07:59 - sqlalchemy.engine.Engine - INFO - [generated in 0.00062s] {}
2025-08-03 21:07:59 - sqlalchemy.engine.Engine - INFO - SELECT contents.id, contents.title, contents.content, contents.content_type, contents.summary, contents.keywords, contents.status, contents.is_featured, contents.is_top, contents.display_order, contents.view_count, contents.publish_time, contents.expire_time, contents.created_by, contents.updated_by, contents.created_at, contents.updated_at 
FROM contents 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 21:07:59 - sqlalchemy.engine.Engine - INFO - [generated in 0.00176s] {'param_1': 0, 'param_2': 20}
2025-08-03 21:07:59 - ContentService - ERROR - 获取记录列表和总数失败: (pymysql.err.OperationalError) (1054, "Unknown column 'contents.summary' in 'field list'")
[SQL: SELECT contents.id, contents.title, contents.content, contents.content_type, contents.summary, contents.keywords, contents.status, contents.is_featured, contents.is_top, contents.display_order, contents.view_count, contents.publish_time, contents.expire_time, contents.created_by, contents.updated_by, contents.created_at, contents.updated_at 
FROM contents 
 LIMIT %(param_1)s, %(param_2)s]
[parameters: {'param_1': 0, 'param_2': 20}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-03 21:07:59 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 21:14:29 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 21:14:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:14:29 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 21:14:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:14:29 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 21:14:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:14:30 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:14:30 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 21:14:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00180s] {'id_1': 1}
2025-08-03 21:14:30 - sqlalchemy.engine.Engine - INFO - SELECT count(contents.id) AS count_1 
FROM contents
2025-08-03 21:14:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00100s] {}
2025-08-03 21:14:30 - sqlalchemy.engine.Engine - INFO - SELECT contents.id, contents.content_type, contents.title, contents.content, contents.is_published, contents.publish_at, contents.created_at, contents.updated_at 
FROM contents 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 21:14:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00091s] {'param_1': 0, 'param_2': 20}
2025-08-03 21:14:30 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 21:21:04 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:21:04 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 21:21:04 - sqlalchemy.engine.Engine - INFO - [cached since 394.8s ago] {'id_1': 1}
2025-08-03 21:21:04 - sqlalchemy.engine.Engine - INFO - INSERT INTO contents (content_type, title, content, is_published, publish_at, created_at, updated_at) VALUES (%(content_type)s, %(title)s, %(content)s, %(is_published)s, %(publish_at)s, %(created_at)s, %(updated_at)s)
2025-08-03 21:21:04 - sqlalchemy.engine.Engine - INFO - [generated in 0.00144s] {'content_type': 'notify', 'title': 'string', 'content': '恭喜xxx获得第一', 'is_published': 1, 'publish_at': datetime.datetime(2025, 8, 3, 13, 17, 27, 838000, tzinfo=TzInfo(UTC)), 'created_at': datetime.datetime(2025, 8, 3, 13, 21, 4, 931665), 'updated_at': datetime.datetime(2025, 8, 3, 13, 21, 4, 931665)}
2025-08-03 21:21:04 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 21:21:05 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:21:05 - sqlalchemy.engine.Engine - INFO - SELECT contents.id, contents.content_type, contents.title, contents.content, contents.is_published, contents.publish_at, contents.created_at, contents.updated_at 
FROM contents 
WHERE contents.id = %(pk_1)s
2025-08-03 21:21:05 - sqlalchemy.engine.Engine - INFO - [generated in 0.00076s] {'pk_1': 1}
2025-08-03 21:21:05 - ContentService - INFO - 创建记录成功 ID=1
2025-08-03 21:21:05 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 21:21:17 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:21:17 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 21:21:17 - sqlalchemy.engine.Engine - INFO - [cached since 407s ago] {'id_1': 1}
2025-08-03 21:21:17 - sqlalchemy.engine.Engine - INFO - INSERT INTO contents (content_type, title, content, is_published, publish_at, created_at, updated_at) VALUES (%(content_type)s, %(title)s, %(content)s, %(is_published)s, %(publish_at)s, %(created_at)s, %(updated_at)s)
2025-08-03 21:21:17 - sqlalchemy.engine.Engine - INFO - [cached since 12.23s ago] {'content_type': 'notify', 'title': 'string', 'content': '恭喜xxx获得第一', 'is_published': 1, 'publish_at': datetime.datetime(2025, 8, 3, 13, 17, 27, 838000, tzinfo=TzInfo(UTC)), 'created_at': datetime.datetime(2025, 8, 3, 13, 21, 17, 160222), 'updated_at': datetime.datetime(2025, 8, 3, 13, 21, 17, 160222)}
2025-08-03 21:21:17 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 21:21:17 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:21:17 - sqlalchemy.engine.Engine - INFO - SELECT contents.id, contents.content_type, contents.title, contents.content, contents.is_published, contents.publish_at, contents.created_at, contents.updated_at 
FROM contents 
WHERE contents.id = %(pk_1)s
2025-08-03 21:21:17 - sqlalchemy.engine.Engine - INFO - [cached since 12.22s ago] {'pk_1': 2}
2025-08-03 21:21:17 - ContentService - INFO - 创建记录成功 ID=2
2025-08-03 21:21:17 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 21:22:30 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:22:30 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 21:22:30 - sqlalchemy.engine.Engine - INFO - [cached since 480s ago] {'id_1': 1}
2025-08-03 21:22:30 - sqlalchemy.engine.Engine - INFO - SELECT count(contents.id) AS count_1 
FROM contents
2025-08-03 21:22:30 - sqlalchemy.engine.Engine - INFO - [cached since 480s ago] {}
2025-08-03 21:22:30 - sqlalchemy.engine.Engine - INFO - SELECT contents.id, contents.content_type, contents.title, contents.content, contents.is_published, contents.publish_at, contents.created_at, contents.updated_at 
FROM contents 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 21:22:30 - sqlalchemy.engine.Engine - INFO - [cached since 480s ago] {'param_1': 0, 'param_2': 20}
2025-08-03 21:22:30 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - [generated in 0.00091s] {'id_1': 1}
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - SELECT contents.id, contents.content_type, contents.title, contents.content, contents.is_published, contents.publish_at, contents.created_at, contents.updated_at 
FROM contents 
WHERE contents.id = %(id_1)s
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - [generated in 0.00125s] {'id_1': 1}
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - UPDATE contents SET content=%(content)s, updated_at=%(updated_at)s WHERE contents.id = %(contents_id)s
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - [generated in 0.00100s] {'content': '恭喜xxx获得第er', 'updated_at': datetime.datetime(2025, 8, 3, 13, 24, 35, 565065), 'contents_id': 1}
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - SELECT contents.id, contents.content_type, contents.title, contents.content, contents.is_published, contents.publish_at, contents.created_at, contents.updated_at 
FROM contents 
WHERE contents.id = %(pk_1)s
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - [generated in 0.00076s] {'pk_1': 1}
2025-08-03 21:24:35 - ContentService - INFO - 更新记录成功 ID=1
2025-08-03 21:24:35 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 21:24:38 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:24:38 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-03 21:24:38 - sqlalchemy.engine.Engine - INFO - [cached since 3.065s ago] {'id_1': 1}
2025-08-03 21:24:38 - sqlalchemy.engine.Engine - INFO - SELECT count(contents.id) AS count_1 
FROM contents
2025-08-03 21:24:38 - sqlalchemy.engine.Engine - INFO - [generated in 0.00093s] {}
2025-08-03 21:24:38 - sqlalchemy.engine.Engine - INFO - SELECT contents.id, contents.content_type, contents.title, contents.content, contents.is_published, contents.publish_at, contents.created_at, contents.updated_at 
FROM contents 
 LIMIT %(param_1)s, %(param_2)s
2025-08-03 21:24:38 - sqlalchemy.engine.Engine - INFO - [generated in 0.00178s] {'param_1': 0, 'param_2': 20}
2025-08-03 21:24:38 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 21:34:25 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 21:34:25 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:34:25 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 21:34:25 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:34:25 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 21:34:25 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 21:34:25 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 21:34:26 - sqlalchemy.engine.Engine - INFO - SELECT contents.id, contents.content_type, contents.title, contents.content, contents.is_published, contents.publish_at, contents.created_at, contents.updated_at 
FROM contents 
WHERE contents.content_type = %(content_type_1)s ORDER BY contents.publish_at DESC, contents.created_at DESC 
 LIMIT %(param_1)s
2025-08-03 21:34:26 - sqlalchemy.engine.Engine - INFO - [generated in 0.00135s] {'content_type_1': <ContentType.NOTIFY: 'notify'>, 'param_1': 3}
2025-08-03 21:34:26 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 21:51:35 - app.main - INFO - 静态文件目录初始化完成: D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:51:43 - app.main - INFO - 静态文件目录初始化完成: D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:51:43 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:51:54 - app.main - INFO - 静态文件目录初始化完成: D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:51:54 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:52:05 - app.main - INFO - 静态文件目录初始化完成: D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:52:05 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:53:39 - app.main - INFO - 静态文件目录初始化完成: D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:53:39 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:55:06 - app.main - INFO - 静态文件目录初始化完成: D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:55:06 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:55:20 - app.main - INFO - 静态文件目录初始化完成: D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:55:20 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:55:30 - app.main - INFO - 静态文件目录初始化完成: D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-03 21:55:30 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 15:07:44 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 15:10:01 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - INSERT INTO feedback (content, user_id, status, admin_reply, created_at, updated_at) VALUES (%(content)s, %(user_id)s, %(status)s, %(admin_reply)s, now(), now())
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - [generated in 0.00082s] {'content': '测试反馈', 'user_id': None, 'status': 'pending', 'admin_reply': None}
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - SELECT feedback.id, feedback.content, feedback.user_id, feedback.status, feedback.admin_reply, feedback.created_at, feedback.updated_at 
FROM feedback 
WHERE feedback.id = %(pk_1)s
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - [generated in 0.00126s] {'pk_1': 1}
2025-08-04 15:11:29 - app.services.feedback_service - INFO - 创建反馈成功 ID=1, user_id=None
2025-08-04 15:11:29 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 15:11:44 - app.core.auth - ERROR - 获取当前用户ID失败: 
2025-08-04 15:13:50 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 15:13:50 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-04 15:13:50 - sqlalchemy.engine.Engine - INFO - [generated in 0.00116s] {'username_1': 'admin'}
2025-08-04 15:13:50 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-04 15:13:50 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-04 15:13:50 - sqlalchemy.engine.Engine - INFO - [generated in 0.00072s] {'last_login_at': datetime.datetime(2025, 8, 4, 7, 13, 50, 889910), 'users_id': 1}
2025-08-04 15:13:51 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-04 15:13:51 - UserService - INFO - 用户认证成功 username=admin
2025-08-04 15:13:52 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 15:13:52 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-04 15:13:52 - sqlalchemy.engine.Engine - INFO - [generated in 0.00120s] {'pk_1': 1}
2025-08-04 15:13:52 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 15:13:57 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 15:13:57 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-04 15:13:57 - sqlalchemy.engine.Engine - INFO - [generated in 0.00152s] {'id_1': 1}
2025-08-04 15:13:57 - sqlalchemy.engine.Engine - INFO - SELECT feedback.id, feedback.content, feedback.user_id, feedback.status, feedback.admin_reply, feedback.created_at, feedback.updated_at 
FROM feedback LEFT OUTER JOIN users ON feedback.user_id = users.id ORDER BY feedback.created_at DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-04 15:13:57 - sqlalchemy.engine.Engine - INFO - [generated in 0.00118s] {'param_1': 0, 'param_2': 20}
2025-08-04 15:13:58 - sqlalchemy.engine.Engine - INFO - SELECT count(feedback.id) AS count_1 
FROM feedback
2025-08-04 15:13:58 - sqlalchemy.engine.Engine - INFO - [generated in 0.00079s] {}
2025-08-04 15:13:58 - app.services.feedback_service - INFO - 获取反馈列表成功，共1条记录，当前页1
2025-08-04 15:13:58 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - [cached since 16.54s ago] {'id_1': 1}
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - INSERT INTO feedback (content, user_id, status, admin_reply, created_at, updated_at) VALUES (%(content)s, %(user_id)s, %(status)s, %(admin_reply)s, now(), now())
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - [cached since 164.7s ago] {'content': '测试反馈', 'user_id': 1, 'status': 'pending', 'admin_reply': None}
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - SELECT feedback.id, feedback.content, feedback.user_id, feedback.status, feedback.admin_reply, feedback.created_at, feedback.updated_at 
FROM feedback 
WHERE feedback.id = %(pk_1)s
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - [cached since 164.7s ago] {'pk_1': 2}
2025-08-04 15:14:14 - app.services.feedback_service - INFO - 创建反馈成功 ID=2, user_id=1
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - [cached since 22.16s ago] {'pk_1': 1}
2025-08-04 15:14:14 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 15:14:16 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 15:14:16 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-04 15:14:16 - sqlalchemy.engine.Engine - INFO - [cached since 19.38s ago] {'id_1': 1}
2025-08-04 15:14:17 - sqlalchemy.engine.Engine - INFO - SELECT feedback.id, feedback.content, feedback.user_id, feedback.status, feedback.admin_reply, feedback.created_at, feedback.updated_at 
FROM feedback LEFT OUTER JOIN users ON feedback.user_id = users.id ORDER BY feedback.created_at DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-04 15:14:17 - sqlalchemy.engine.Engine - INFO - [cached since 19.23s ago] {'param_1': 0, 'param_2': 20}
2025-08-04 15:14:17 - sqlalchemy.engine.Engine - INFO - SELECT count(feedback.id) AS count_1 
FROM feedback
2025-08-04 15:14:17 - sqlalchemy.engine.Engine - INFO - [cached since 18.98s ago] {}
2025-08-04 15:14:17 - app.services.feedback_service - INFO - 获取反馈列表成功，共2条记录，当前页1
2025-08-04 15:14:17 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:29 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 17:41:45 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 17:42:07 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0c1mq5100P2GJU1twc3002iGUZ1mq51a&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-04 17:42:07 - app.utils.wechat - ERROR - 微信小程序登录失败: 40163 - code been used, rid: 6890806f-16b6ed50-0f7f633b
2025-08-04 17:42:07 - app.utils.wechat - ERROR - 小程序登录异常: 微信API错误: code been used, rid: 6890806f-16b6ed50-0f7f633b
2025-08-04 17:43:17 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0c16mt0w3wpro53Vsc2w3QpLtg16mt0U&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-04 17:43:17 - app.utils.wechat - INFO - 成功获取小程序session openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-04 17:43:17 - app.utils.wechat - INFO - 小程序登录成功 openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.wechat_openid = %(wechat_openid_1)s
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - [generated in 0.00271s] {'wechat_openid_1': 'osn-d4nN8AYr6h1LNrHCwK1uD1Pw'}
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - [generated in 0.00130s] {'last_login_at': datetime.datetime(2025, 8, 4, 9, 43, 18, 580970), 'users_id': 2}
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - [generated in 0.00344s] {'pk_1': 2}
2025-08-04 17:43:18 - UserService - INFO - 微信用户登录成功 openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-04 17:43:18 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 17:47:45 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 17:47:51 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0c16mt0w3wpro53Vsc2w3QpLtg16mt0U&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-04 17:47:51 - app.utils.wechat - ERROR - 微信小程序登录失败: 40163 - code been used, rid: 689081c7-15ca8b98-5a2fa1ad
2025-08-04 17:47:51 - app.utils.wechat - ERROR - 小程序登录异常: 微信API错误: code been used, rid: 689081c7-15ca8b98-5a2fa1ad
2025-08-04 17:48:19 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0d15Ft0w3eGro53vG64w3hFGbD15Ft0S&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-04 17:48:19 - app.utils.wechat - INFO - 成功获取小程序session openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-04 17:48:19 - app.utils.wechat - INFO - 小程序登录成功 openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-04 17:48:19 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-04 17:48:19 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.wechat_openid = %(wechat_openid_1)s
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - [generated in 0.00244s] {'wechat_openid_1': 'osn-d4nN8AYr6h1LNrHCwK1uD1Pw'}
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - [generated in 0.00210s] {'last_login_at': datetime.datetime(2025, 8, 4, 9, 48, 20, 331040), 'users_id': 2}
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - [generated in 0.00186s] {'pk_1': 2}
2025-08-04 17:48:20 - UserService - INFO - 微信用户登录成功 openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-04 17:48:20 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 17:48:40 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 17:49:55 - httpx - INFO - HTTP Request: GET https://api.weixin.qq.com/sns/jscode2session?appid=wx7f35b7f5d08c142f&secret=f55c22f4142628233622ff301918ae24&js_code=0d19Xz1w3z6yp53qim1w3lboU149Xz1j&grant_type=authorization_code "HTTP/1.1 200 OK"
2025-08-04 17:49:55 - app.utils.wechat - INFO - 成功获取小程序session openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-04 17:49:55 - app.utils.wechat - INFO - 小程序登录成功 openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.wechat_openid = %(wechat_openid_1)s
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - [generated in 0.00205s] {'wechat_openid_1': 'osn-d4nN8AYr6h1LNrHCwK1uD1Pw'}
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - [generated in 0.00112s] {'last_login_at': datetime.datetime(2025, 8, 4, 9, 49, 56, 547672), 'users_id': 2}
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - [generated in 0.00263s] {'pk_1': 2}
2025-08-04 17:49:56 - UserService - INFO - 微信用户登录成功 openid=osn-d4nN8AYr6h1LNrHCwK1uD1Pw
2025-08-04 17:49:56 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 22:17:23 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:24:17 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:33:37 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:34:35 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:34:56 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:34:56 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:58:09 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:58:09 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 23:00:34 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 23:00:34 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 23:01:05 - app.core.auth - ERROR - 获取当前用户ID失败: 
2025-08-04 23:01:11 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-04 23:01:11 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 23:01:11 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-04 23:01:11 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 23:01:11 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-04 23:01:11 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 23:01:12 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 23:01:12 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-04 23:01:12 - sqlalchemy.engine.Engine - INFO - [generated in 0.00193s] {'username_1': 'admin'}
2025-08-04 23:01:12 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-04 23:01:12 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-04 23:01:12 - sqlalchemy.engine.Engine - INFO - [generated in 0.00074s] {'last_login_at': datetime.datetime(2025, 8, 4, 15, 1, 12, 445418), 'users_id': 1}
2025-08-04 23:01:12 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-04 23:01:12 - UserService - INFO - 用户认证成功 username=admin
2025-08-04 23:01:12 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 23:01:12 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-04 23:01:12 - sqlalchemy.engine.Engine - INFO - [generated in 0.00154s] {'pk_1': 1}
2025-08-04 23:01:12 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 23:01:52 - app.utils.image_handler - ERROR - 创建缩略图失败 small: [Errno 2] No such file or directory: 'D:\\h5_code\\yysls\\yysls-ranking-backend\\uploads\\thumbnails\\temp\\thumb_small_temp_20250804_230152_c9f200ec_1.jpg'
2025-08-04 23:01:52 - app.utils.image_handler - ERROR - 创建缩略图失败 medium: [Errno 2] No such file or directory: 'D:\\h5_code\\yysls\\yysls-ranking-backend\\uploads\\thumbnails\\temp\\thumb_medium_temp_20250804_230152_c9f200ec_1.jpg'
2025-08-04 23:01:52 - app.utils.image_handler - ERROR - 创建缩略图失败 large: [Errno 2] No such file or directory: 'D:\\h5_code\\yysls\\yysls-ranking-backend\\uploads\\thumbnails\\temp\\thumb_large_temp_20250804_230152_c9f200ec_1.jpg'
2025-08-04 23:01:52 - app.utils.image_handler - INFO - 图片保存成功: temp_20250804_230152_c9f200ec_1.jpg, 用户: 1, 分类: temp
2025-08-04 23:01:52 - app.api.v1.endpoints.upload - INFO - 用户1上传图片文件: e2d0814a693649b1402a36492395cc1.jpg -> temp_20250804_230152_c9f200ec_1.jpg
2025-08-04 23:02:22 - app.api.v1.endpoints.upload - INFO - 用户1上传Excel文件: template.xlsx -> 8e58601083d4460386271e2cecb5aa97_1.xlsx
2025-08-04 22:28:02 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:28:02 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:28:20 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:28:20 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:29:08 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:29:08 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:29:29 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:29:29 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:29:57 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:29:57 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:30:15 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:30:15 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:30:32 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:30:32 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:33:45 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:33:45 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:33:58 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:33:58 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:34:09 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:34:09 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:36:31 - app.api.v1.endpoints.upload - INFO - 用户1临时上传Excel文件: template.xlsx -> 5e48be83a07144fb9d80fb8ca38751b3_1.xlsx
2025-08-04 22:37:11 - app.utils.image_handler - ERROR - 创建缩略图失败 small: [Errno 2] No such file or directory: 'D:\\h5_code\\yysls\\yysls-ranking-backend\\uploads\\thumbnails\\temp\\thumb_small_temp_20250804_223711_2e7ee2f4_1.jpg'
2025-08-04 22:37:11 - app.utils.image_handler - ERROR - 创建缩略图失败 medium: [Errno 2] No such file or directory: 'D:\\h5_code\\yysls\\yysls-ranking-backend\\uploads\\thumbnails\\temp\\thumb_medium_temp_20250804_223711_2e7ee2f4_1.jpg'
2025-08-04 22:37:11 - app.utils.image_handler - ERROR - 创建缩略图失败 large: [Errno 2] No such file or directory: 'D:\\h5_code\\yysls\\yysls-ranking-backend\\uploads\\thumbnails\\temp\\thumb_large_temp_20250804_223711_2e7ee2f4_1.jpg'
2025-08-04 22:37:11 - app.utils.image_handler - INFO - 图片保存成功: temp_20250804_223711_2e7ee2f4_1.jpg, 用户: 1, 分类: temp
2025-08-04 22:37:11 - app.api.v1.endpoints.upload - INFO - 用户1上传图片文件: 微信图片_20250804145514.jpg -> temp_20250804_223711_2e7ee2f4_1.jpg
2025-08-04 22:46:17 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:46:17 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:46:27 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:46:27 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:46:37 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:46:37 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:46:50 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:46:50 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:47:06 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:47:06 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:48:15 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-04 22:48:15 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 22:48:15 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-04 22:48:15 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 22:48:15 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-04 22:48:15 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 22:48:15 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 22:48:15 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-04 22:48:15 - sqlalchemy.engine.Engine - INFO - [generated in 0.00114s] {'id_1': 1}
2025-08-04 22:48:15 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 22:48:19 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 22:48:19 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-04 22:48:19 - sqlalchemy.engine.Engine - INFO - [cached since 3.991s ago] {'id_1': 1}
2025-08-04 22:48:19 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 22:48:26 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 22:48:26 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-04 22:48:26 - sqlalchemy.engine.Engine - INFO - [cached since 10.67s ago] {'id_1': 1}
2025-08-04 22:48:26 - sqlalchemy.engine.Engine - INFO - SELECT feedback.id, feedback.content, feedback.user_id, feedback.status, feedback.admin_reply, feedback.created_at, feedback.updated_at 
FROM feedback LEFT OUTER JOIN users ON feedback.user_id = users.id ORDER BY feedback.created_at DESC 
 LIMIT %(param_1)s, %(param_2)s
2025-08-04 22:48:26 - sqlalchemy.engine.Engine - INFO - [generated in 0.00094s] {'param_1': 0, 'param_2': 20}
2025-08-04 22:48:26 - sqlalchemy.engine.Engine - INFO - SELECT count(feedback.id) AS count_1 
FROM feedback
2025-08-04 22:48:26 - sqlalchemy.engine.Engine - INFO - [generated in 0.00073s] {}
2025-08-04 22:48:26 - app.services.feedback_service - INFO - 获取反馈列表成功，共2条记录，当前页1
2025-08-04 22:48:26 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 22:48:49 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 22:48:49 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 22:53:29 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-04 22:53:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00161s] {'id_1': 1}
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - SELECT count(system_configs.id) AS count_1 
FROM system_configs
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00065s] {}
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs ORDER BY system_configs.config_type, system_configs.config_key 
 LIMIT %(param_1)s, %(param_2)s
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00120s] {'param_1': 0, 'param_2': 20}
2025-08-04 22:53:30 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 22:54:12 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 22:54:12 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.id = %(id_1)s
2025-08-04 22:54:12 - sqlalchemy.engine.Engine - INFO - [cached since 42.26s ago] {'id_1': 1}
2025-08-04 22:54:12 - sqlalchemy.engine.Engine - INFO - SELECT count(system_configs.id) AS count_1 
FROM system_configs
2025-08-04 22:54:12 - sqlalchemy.engine.Engine - INFO - [cached since 42.26s ago] {}
2025-08-04 22:54:12 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs ORDER BY system_configs.config_type, system_configs.config_key 
 LIMIT %(param_1)s, %(param_2)s
2025-08-04 22:54:12 - sqlalchemy.engine.Engine - INFO - [cached since 42.25s ago] {'param_1': 0, 'param_2': 10}
2025-08-04 22:54:12 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 22:54:31 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 22:54:31 - sqlalchemy.engine.Engine - INFO - SELECT system_configs.id, system_configs.config_key, system_configs.config_value, system_configs.config_type, system_configs.name, system_configs.description, system_configs.is_public, system_configs.created_at, system_configs.updated_at 
FROM system_configs 
WHERE system_configs.is_public = true
2025-08-04 22:54:31 - sqlalchemy.engine.Engine - INFO - [generated in 0.00073s] {}
2025-08-04 22:54:31 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 23:20:13 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 23:20:13 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 23:20:13 - app.main - INFO - 上传目录绝对路径: D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 23:20:13 - app.main - INFO - 上传目录是否存在: True
2025-08-04 23:20:13 - app.main - INFO - 图片临时目录: D:\h5_code\yysls\yysls-ranking-backend\uploads\images\temp
2025-08-04 23:20:13 - app.main - INFO - 图片临时目录是否存在: True
2025-08-04 23:20:13 - app.main - INFO - 图片临时目录文件数量: 2
2025-08-04 23:20:37 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 23:20:37 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 23:20:37 - app.main - INFO - 上传目录绝对路径: D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 23:20:37 - app.main - INFO - 上传目录是否存在: True
2025-08-04 23:20:37 - app.main - INFO - 图片临时目录: D:\h5_code\yysls\yysls-ranking-backend\uploads\images\temp
2025-08-04 23:20:37 - app.main - INFO - 图片临时目录是否存在: True
2025-08-04 23:20:37 - app.main - INFO - 图片临时目录文件数量: 2
2025-08-04 23:22:17 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 23:22:17 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 23:22:17 - app.main - INFO - 上传目录绝对路径: D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 23:22:17 - app.main - INFO - 上传目录是否存在: True
2025-08-04 23:22:17 - app.main - INFO - 图片临时目录: D:\h5_code\yysls\yysls-ranking-backend\uploads\images\temp
2025-08-04 23:22:17 - app.main - INFO - 图片临时目录是否存在: True
2025-08-04 23:22:17 - app.main - INFO - 图片临时目录文件数量: 2
2025-08-04 23:22:59 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 23:22:59 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-04 23:33:10 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-04 23:33:10 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:11:09 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:11:09 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:11:34 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:11:34 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:12:59 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:12:59 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - [generated in 0.00108s] {'id_1': 1}
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.team_name, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s ORDER BY ranking_details.completion_seconds
2025-08-05 10:18:42 - sqlalchemy.engine.Engine - INFO - [generated in 0.00196s] {'ranking_id_1': 1}
2025-08-05 10:18:43 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-05 10:18:58 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:18:58 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-05 10:18:58 - sqlalchemy.engine.Engine - INFO - [generated in 0.00120s] {}
2025-08-05 10:18:59 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-05 10:18:59 - sqlalchemy.engine.Engine - INFO - [generated in 0.00135s] {'param_1': 0, 'param_2': 20}
2025-08-05 10:18:59 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-05 10:19:37 - app.core.auth - ERROR - 获取当前用户ID失败: 
2025-08-05 10:19:43 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:19:43 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-05 10:19:43 - sqlalchemy.engine.Engine - INFO - [generated in 0.00171s] {'username_1': 'admin'}
2025-08-05 10:19:43 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-05 10:19:44 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-05 10:19:44 - sqlalchemy.engine.Engine - INFO - [generated in 0.00104s] {'last_login_at': datetime.datetime(2025, 8, 5, 2, 19, 44, 174843), 'users_id': 1}
2025-08-05 10:19:44 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:19:44 - UserService - INFO - 用户认证成功 username=admin
2025-08-05 10:19:44 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:19:44 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-05 10:19:44 - sqlalchemy.engine.Engine - INFO - [generated in 0.00194s] {'pk_1': 1}
2025-08-05 10:19:44 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-05 10:19:49 - app.api.v1.endpoints.upload - INFO - 用户1上传Excel文件: template.xlsx -> 3ecc2dfdb90a41f6ae34069a2af1173e_1.xlsx
2025-08-05 10:20:03 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:20:03 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:20:03 - sqlalchemy.engine.Engine - INFO - [cached since 80.34s ago] {'id_1': 1}
2025-08-05 10:20:03 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:20:03 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:20:03 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:20:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00111s] {'pk_1': 1}
2025-08-05 10:20:03 - RankingService - INFO - 更新记录成功 ID=1
2025-08-05 10:20:03 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-05 10:20:48 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:20:48 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:20:48 - sqlalchemy.engine.Engine - INFO - [cached since 125.2s ago] {'id_1': 1}
2025-08-05 10:20:48 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:20:48 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:20:48 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:20:48 - sqlalchemy.engine.Engine - INFO - [cached since 44.79s ago] {'pk_1': 1}
2025-08-05 10:20:48 - RankingService - INFO - 更新记录成功 ID=1
2025-08-05 10:20:48 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-05 10:22:16 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:22:16 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:22:16 - sqlalchemy.engine.Engine - INFO - [cached since 214.1s ago] {'id_1': 1}
2025-08-05 10:22:17 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:22:17 - sqlalchemy.engine.Engine - INFO - [cached since 214.2s ago] {'id_1': 1}
2025-08-05 10:22:17 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:22:17 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:22:17 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:22:17 - sqlalchemy.engine.Engine - INFO - [cached since 133.8s ago] {'pk_1': 1}
2025-08-05 10:22:17 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-05 10:22:17 - sqlalchemy.engine.Engine - INFO - [generated in 0.00087s] {'ranking_id_1': 1}
2025-08-05 10:22:17 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:22:17 - RankingService - ERROR - 导入Excel榜单明细失败 ranking_id=1: 
2025-08-05 10:22:17 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: 
2025-08-05 10:23:09 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:23:09 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:23:09 - sqlalchemy.engine.Engine - INFO - [cached since 266.9s ago] {'id_1': 1}
2025-08-05 10:23:09 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:23:09 - sqlalchemy.engine.Engine - INFO - [cached since 267s ago] {'id_1': 1}
2025-08-05 10:23:09 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:23:10 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:23:10 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:23:10 - sqlalchemy.engine.Engine - INFO - [cached since 186.6s ago] {'pk_1': 1}
2025-08-05 10:23:10 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-05 10:23:10 - sqlalchemy.engine.Engine - INFO - [cached since 52.74s ago] {'ranking_id_1': 1}
2025-08-05 10:23:10 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:23:10 - RankingService - ERROR - 导入Excel榜单明细失败 ranking_id=1: 
2025-08-05 10:23:10 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: 
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - [cached since 286.4s ago] {'id_1': 1}
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - [cached since 286.5s ago] {'id_1': 1}
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - [cached since 206s ago] {'pk_1': 1}
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - [cached since 72.19s ago] {'ranking_id_1': 1}
2025-08-05 10:23:29 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:23:30 - RankingService - ERROR - 导入Excel榜单明细失败 ranking_id=1: 
2025-08-05 10:23:30 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: 
2025-08-05 10:25:00 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:25:00 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:25:09 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:25:09 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:25:20 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:25:20 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:25:39 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:25:39 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:25:57 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:25:57 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:26:27 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:26:27 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:26:46 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:26:46 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:27:07 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:27:07 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:27:19 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:27:19 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:27:34 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:27:34 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:27:52 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:27:52 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:28:01 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-05 10:28:01 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:28:01 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-05 10:28:01 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:28:01 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-05 10:28:01 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:28:01 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:28:01 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:28:01 - sqlalchemy.engine.Engine - INFO - [generated in 0.00190s] {'id_1': 1}
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - [cached since 0.08492s ago] {'id_1': 1}
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - [generated in 0.00118s] {'pk_1': 1}
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - [generated in 0.00085s] {'ranking_id_1': 1}
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:28:02 - app.utils.excel_handler - INFO - 成功解析Excel文件，共15条记录
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:02 - sqlalchemy.engine.Engine - INFO - [generated in 0.00176s] {'ranking_id': 1, 'rank_start': 1, 'rank_end': 2, 'completion_time': datetime.time(1, 44), 'completion_seconds': 2640, 'participant_count': 2, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 2, 956174), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 2, 956174)}
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.1011s ago] {'ranking_id': 1, 'rank_start': 3, 'rank_end': 9, 'completion_time': datetime.time(1, 45), 'completion_seconds': 2700, 'participant_count': 7, 'team_info': '队伍F,队伍G,队伍H,队伍I,队伍J', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 56479), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 56479)}
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.2017s ago] {'ranking_id': 1, 'rank_start': 10, 'rank_end': 19, 'completion_time': datetime.time(1, 46), 'completion_seconds': 2760, 'participant_count': 10, 'team_info': '队伍K,队伍L,队伍M,队伍N,队伍O', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 156788), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 156788)}
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.3042s ago] {'ranking_id': 1, 'rank_start': 20, 'rank_end': 32, 'completion_time': datetime.time(1, 47), 'completion_seconds': 2820, 'participant_count': 13, 'team_info': '队伍P,队伍Q,队伍R,队伍S,队伍T', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 259718), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 259718)}
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.4069s ago] {'ranking_id': 1, 'rank_start': 33, 'rank_end': 40, 'completion_time': datetime.time(1, 48), 'completion_seconds': 2880, 'participant_count': 8, 'team_info': '队伍U,队伍V,队伍W,队伍X,队伍Y', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 362004), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 362004)}
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.5091s ago] {'ranking_id': 1, 'rank_start': 41, 'rank_end': 48, 'completion_time': datetime.time(1, 49), 'completion_seconds': 2940, 'participant_count': 8, 'team_info': None, 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 464357), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 464357)}
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.6118s ago] {'ranking_id': 1, 'rank_start': 49, 'rank_end': 54, 'completion_time': datetime.time(1, 50), 'completion_seconds': 3000, 'participant_count': 6, 'team_info': None, 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 567430), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 567430)}
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.7142s ago] {'ranking_id': 1, 'rank_start': 55, 'rank_end': 61, 'completion_time': datetime.time(1, 51), 'completion_seconds': 3060, 'participant_count': 7, 'team_info': None, 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 669779), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 669779)}
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.8156s ago] {'ranking_id': 1, 'rank_start': 62, 'rank_end': 64, 'completion_time': datetime.time(1, 52), 'completion_seconds': 3120, 'participant_count': 3, 'team_info': None, 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 771423), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 771423)}
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - [cached since 0.9188s ago] {'ranking_id': 1, 'rank_start': 65, 'rank_end': 68, 'completion_time': datetime.time(1, 53), 'completion_seconds': 3180, 'participant_count': 4, 'team_info': None, 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 874125), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 874125)}
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:03 - sqlalchemy.engine.Engine - INFO - [cached since 1.021s ago] {'ranking_id': 1, 'rank_start': 69, 'rank_end': 69, 'completion_time': datetime.time(1, 54), 'completion_seconds': 3240, 'participant_count': 1, 'team_info': None, 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 975887), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 3, 975887)}
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - [cached since 1.125s ago] {'ranking_id': 1, 'rank_start': 70, 'rank_end': 72, 'completion_time': datetime.time(1, 56), 'completion_seconds': 3360, 'participant_count': 3, 'team_info': None, 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 4, 80295), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 4, 80295)}
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - [cached since 1.227s ago] {'ranking_id': 1, 'rank_start': 73, 'rank_end': 73, 'completion_time': datetime.time(1, 57), 'completion_seconds': 3420, 'participant_count': 1, 'team_info': None, 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 4, 181580), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 4, 181580)}
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - [cached since 1.328s ago] {'ranking_id': 1, 'rank_start': 74, 'rank_end': 74, 'completion_time': datetime.time(1, 58), 'completion_seconds': 3480, 'participant_count': 1, 'team_info': None, 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 4, 283614), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 4, 283614)}
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - [cached since 1.433s ago] {'ranking_id': 1, 'rank_start': 493, 'rank_end': 500, 'completion_time': datetime.time(3, 35), 'completion_seconds': 2100, 'participant_count': 8, 'team_info': None, 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 28, 4, 387326), 'updated_at': datetime.datetime(2025, 8, 5, 2, 28, 4, 387326)}
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:28:04 - RankingService - INFO - 成功导入15条榜单明细数据到榜单1
2025-08-05 10:28:04 - RankingService - INFO - 清理临时Excel文件: D:\h5_code\yysls\yysls-ranking-backend\uploads\temp\excel\3ecc2dfdb90a41f6ae34069a2af1173e_1.xlsx
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id AS rankings_id, rankings.name AS rankings_name, rankings.period AS rankings_period, rankings.ranking_type AS rankings_ranking_type, rankings.start_time AS rankings_start_time, rankings.end_time AS rankings_end_time, rankings.team_size_limit AS rankings_team_size_limit, rankings.total_participants AS rankings_total_participants, rankings.status AS rankings_status, rankings.created_at AS rankings_created_at, rankings.updated_at AS rankings_updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - [generated in 0.00118s] {'pk_1': 1}
2025-08-05 10:28:04 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-05 10:28:20 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:28:20 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:28:43 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:28:43 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:29:03 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:29:03 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:30:27 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:30:27 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:30:44 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:30:44 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - [generated in 0.00109s] {'id_1': 1}
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - [cached since 0.08687s ago] {'id_1': 1}
2025-08-05 10:31:00 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:31:01 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:31:01 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:31:01 - sqlalchemy.engine.Engine - INFO - [generated in 0.00147s] {'pk_1': 1}
2025-08-05 10:31:01 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-05 10:31:10 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:31:10 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:31:27 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:31:27 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:32:57 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-05 10:32:57 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - [generated in 0.00153s] {'id_1': 1}
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - [cached since 0.07741s ago] {'id_1': 1}
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - UPDATE rankings SET start_time=%(start_time)s, end_time=%(end_time)s, updated_at=%(updated_at)s WHERE rankings.id = %(rankings_id)s
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - [generated in 0.00119s] {'start_time': datetime.datetime(2025, 8, 1, 0, 0), 'end_time': datetime.datetime(2025, 8, 15, 0, 0), 'updated_at': datetime.datetime(2025, 8, 5, 2, 32, 58, 591640), 'rankings_id': 1}
2025-08-05 10:32:58 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:32:59 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:32:59 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:32:59 - sqlalchemy.engine.Engine - INFO - [generated in 0.00128s] {'pk_1': 1}
2025-08-05 10:32:59 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-05 10:33:01 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 10:33:01 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 10:35:54 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-05 10:35:54 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - [generated in 0.00142s] {'id_1': 1}
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - [cached since 0.07862s ago] {'id_1': 1}
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:35:55 - sqlalchemy.engine.Engine - INFO - [generated in 0.00146s] {'pk_1': 1}
2025-08-05 10:35:56 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-05 10:36:13 - app.api.v1.endpoints.upload - INFO - 用户1上传Excel文件: template.xlsx -> 8a6a4a0599514732a7e4a098a4b643d4_1.xlsx
2025-08-05 10:36:24 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:36:24 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:36:24 - sqlalchemy.engine.Engine - INFO - [cached since 29.26s ago] {'id_1': 1}
2025-08-05 10:36:24 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-05 10:36:24 - sqlalchemy.engine.Engine - INFO - [cached since 29.37s ago] {'id_1': 1}
2025-08-05 10:36:24 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - [cached since 29.29s ago] {'pk_1': 1}
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - DELETE FROM ranking_details WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - [generated in 0.00102s] {'ranking_id_1': 1}
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:36:25 - app.utils.excel_handler - INFO - 成功解析Excel文件，共15条记录
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - [generated in 0.00164s] {'ranking_id': 1, 'rank_start': 1, 'rank_end': 2, 'completion_time': datetime.time(0, 1, 44), 'completion_seconds': 104, 'participant_count': 2, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 25, 856991), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 25, 856991)}
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:25 - sqlalchemy.engine.Engine - INFO - [cached since 0.09966s ago] {'ranking_id': 1, 'rank_start': 3, 'rank_end': 9, 'completion_time': datetime.time(0, 1, 45), 'completion_seconds': 105, 'participant_count': 7, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 25, 954972), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 25, 954972)}
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - [cached since 0.2031s ago] {'ranking_id': 1, 'rank_start': 10, 'rank_end': 19, 'completion_time': datetime.time(0, 1, 46), 'completion_seconds': 106, 'participant_count': 10, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 58305), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 58305)}
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - [cached since 0.3099s ago] {'ranking_id': 1, 'rank_start': 20, 'rank_end': 32, 'completion_time': datetime.time(0, 1, 47), 'completion_seconds': 107, 'participant_count': 13, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 165768), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 165768)}
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - [cached since 0.4057s ago] {'ranking_id': 1, 'rank_start': 33, 'rank_end': 40, 'completion_time': datetime.time(0, 1, 48), 'completion_seconds': 108, 'participant_count': 8, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 261216), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 261216)}
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - [cached since 0.5096s ago] {'ranking_id': 1, 'rank_start': 41, 'rank_end': 48, 'completion_time': datetime.time(0, 1, 49), 'completion_seconds': 109, 'participant_count': 8, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 365670), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 365670)}
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - [cached since 0.6129s ago] {'ranking_id': 1, 'rank_start': 49, 'rank_end': 54, 'completion_time': datetime.time(0, 1, 50), 'completion_seconds': 110, 'participant_count': 6, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 468676), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 468676)}
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - [cached since 0.7213s ago] {'ranking_id': 1, 'rank_start': 55, 'rank_end': 61, 'completion_time': datetime.time(0, 1, 51), 'completion_seconds': 111, 'participant_count': 7, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 577089), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 577089)}
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - [cached since 0.8244s ago] {'ranking_id': 1, 'rank_start': 62, 'rank_end': 64, 'completion_time': datetime.time(0, 1, 52), 'completion_seconds': 112, 'participant_count': 3, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 680207), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 680207)}
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - [cached since 0.9193s ago] {'ranking_id': 1, 'rank_start': 65, 'rank_end': 68, 'completion_time': datetime.time(0, 1, 53), 'completion_seconds': 113, 'participant_count': 4, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 774724), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 774724)}
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - [cached since 1.021s ago] {'ranking_id': 1, 'rank_start': 69, 'rank_end': 69, 'completion_time': datetime.time(0, 1, 54), 'completion_seconds': 114, 'participant_count': 1, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 877458), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 877458)}
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:26 - sqlalchemy.engine.Engine - INFO - [cached since 1.126s ago] {'ranking_id': 1, 'rank_start': 70, 'rank_end': 72, 'completion_time': datetime.time(0, 1, 56), 'completion_seconds': 116, 'participant_count': 3, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 981167), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 26, 981167)}
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - [cached since 1.226s ago] {'ranking_id': 1, 'rank_start': 73, 'rank_end': 73, 'completion_time': datetime.time(0, 1, 57), 'completion_seconds': 117, 'participant_count': 1, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 27, 80750), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 27, 80750)}
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - [cached since 1.328s ago] {'ranking_id': 1, 'rank_start': 74, 'rank_end': 74, 'completion_time': datetime.time(0, 1, 58), 'completion_seconds': 118, 'participant_count': 1, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 27, 183425), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 27, 183425)}
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - INSERT INTO ranking_details (ranking_id, rank_start, rank_end, completion_time, completion_seconds, participant_count, team_info, team_name, created_at, updated_at) VALUES (%(ranking_id)s, %(rank_start)s, %(rank_end)s, %(completion_time)s, %(completion_seconds)s, %(participant_count)s, %(team_info)s, %(team_name)s, %(created_at)s, %(updated_at)s)
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - [cached since 1.432s ago] {'ranking_id': 1, 'rank_start': 493, 'rank_end': 500, 'completion_time': datetime.time(0, 3, 35), 'completion_seconds': 215, 'participant_count': 8, 'team_info': '队伍A,队伍B,队伍C,队伍D,队伍E', 'team_name': '星梦彩云归的队伍', 'created_at': datetime.datetime(2025, 8, 5, 2, 36, 27, 287759), 'updated_at': datetime.datetime(2025, 8, 5, 2, 36, 27, 287759)}
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-05 10:36:27 - RankingService - INFO - 成功导入15条榜单明细数据到榜单1
2025-08-05 10:36:27 - RankingService - INFO - 清理临时Excel文件: D:\h5_code\yysls\yysls-ranking-backend\uploads\temp\excel\8a6a4a0599514732a7e4a098a4b643d4_1.xlsx
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id AS rankings_id, rankings.name AS rankings_name, rankings.period AS rankings_period, rankings.ranking_type AS rankings_ranking_type, rankings.start_time AS rankings_start_time, rankings.end_time AS rankings_end_time, rankings.team_size_limit AS rankings_team_size_limit, rankings.total_participants AS rankings_total_participants, rankings.status AS rankings_status, rankings.created_at AS rankings_created_at, rankings.updated_at AS rankings_updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - [generated in 0.00110s] {'pk_1': 1}
2025-08-05 10:36:27 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-05 13:19:22 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 13:19:22 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 13:22:37 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 13:22:37 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 13:27:05 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 13:27:05 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
2025-08-05 13:27:17 - app.main - INFO - 静态文件服务已启用: /static -> D:\h5_code\yysls\yysls-ranking-backend\static
2025-08-05 13:27:17 - app.main - INFO - 上传文件服务已启用: /uploads -> D:\h5_code\yysls\yysls-ranking-backend\uploads
