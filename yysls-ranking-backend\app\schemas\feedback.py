"""
用户反馈相关的Pydantic模型
"""
from datetime import datetime
from typing import Optional, List
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, field_validator

from app.models.feedback import FeedbackStatus


class FeedbackBase(BaseModel):
    """反馈基础模型"""
    content: str = Field(..., min_length=1, max_length=1000, description="反馈内容，最大1000字符")
    user_id: Optional[int] = Field(None, description="用户ID，可为空支持匿名反馈")

    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        if not v or not v.strip():
            raise ValueError('反馈内容不能为空')
        if len(v.strip()) > 1000:
            raise ValueError('反馈内容不能超过1000字符')
        return v.strip()


class FeedbackCreate(FeedbackBase):
    """创建反馈的请求模型"""
    pass


class FeedbackUpdate(BaseModel):
    """更新反馈的请求模型（管理员使用）"""
    status: Optional[FeedbackStatus] = Field(None, description="反馈状态")
    admin_reply: Optional[str] = Field(None, max_length=2000, description="管理员回复，最大2000字符")

    @field_validator('admin_reply')
    @classmethod
    def validate_admin_reply(cls, v):
        if v is not None:
            v = v.strip()
            if len(v) > 2000:
                raise ValueError('管理员回复不能超过2000字符')
            return v if v else None
        return v


class FeedbackResponse(FeedbackBase):
    """反馈响应模型"""
    id: int = Field(description="反馈ID")
    status: FeedbackStatus = Field(description="反馈状态")
    admin_reply: Optional[str] = Field(None, description="管理员回复")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    # 用户信息（如果不是匿名反馈）
    user_nickname: Optional[str] = Field(None, description="用户昵称")
    user_username: Optional[str] = Field(None, description="用户名")
    avatar_url: Optional[str] = Field(None, description="用户头像URL")

    model_config = ConfigDict(from_attributes=True)


class FeedbackListQuery(BaseModel):
    """反馈列表查询参数"""
    status: Optional[FeedbackStatus] = Field(None, description="按状态筛选")
    user_id: Optional[int] = Field(None, description="按用户ID筛选")
    page: int = Field(default=1, ge=1, description="页码，从1开始")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量，最大100")
    
    # 排序参数
    order_by: str = Field(default="created_at", description="排序字段")
    order_desc: bool = Field(default=True, description="是否降序排列")

    @field_validator('order_by')
    @classmethod
    def validate_order_by(cls, v):
        allowed_fields = ['id', 'created_at', 'updated_at', 'status']
        if v not in allowed_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(allowed_fields)}')
        return v


class FeedbackListResponse(BaseModel):
    """反馈列表响应模型"""
    items: List[FeedbackResponse] = Field(description="反馈列表")
    total: int = Field(description="总数量")
    page: int = Field(description="当前页码")
    page_size: int = Field(description="每页数量")
    total_pages: int = Field(description="总页数")


class FeedbackStatsResponse(BaseModel):
    """反馈统计响应模型"""
    total_count: int = Field(description="总反馈数")
    pending_count: int = Field(description="待处理数")
    processing_count: int = Field(description="处理中数")
    resolved_count: int = Field(description="已处理数")
    ignored_count: int = Field(description="已忽略数")
    today_count: int = Field(description="今日新增数")
    week_count: int = Field(description="本周新增数")
